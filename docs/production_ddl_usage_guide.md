# 生产环境DDL文件使用指南

## 文件概述

**文件名**: `filtered_production_cb_script.sql`  
**生成时间**: 2025-08-25 13:45:46  
**文件大小**: 31,994 行  
**包含表数**: 291 个生产环境表  

## 文件特点

### ✅ 已排除的测试表类型
- **备份表**: 所有包含 'bak' 标识的表
- **历史版本表**: 所有带日期后缀的表（如20220729、20240823等）
- **模板表**: 所有包含 'template'/'tmpl' 的表
- **临时表**: 所有包含 'temp'/'tmp' 的表

### ✅ 包含的内容
- **291个核心业务表**: 完整的表结构定义
- **相关约束**: 主键、外键、检查约束
- **索引定义**: 聚集索引和非聚集索引
- **扩展属性**: 表和字段的描述信息
- **数据库设置**: USE、SET等必要的数据库配置

## 使用方法

### 1. 数据库准备
```sql
-- 创建目标数据库（如果不存在）
CREATE DATABASE [qtcc_cb]
GO

-- 切换到目标数据库
USE [qtcc_cb]
GO
```

### 2. 执行DDL脚本
```bash
# 方法1: 使用SQL Server Management Studio
# 1. 打开 filtered_production_cb_script.sql
# 2. 连接到目标数据库服务器
# 3. 确保当前数据库为 qtcc_cb
# 4. 执行脚本

# 方法2: 使用sqlcmd命令行
sqlcmd -S server_name -d qtcc_cb -i filtered_production_cb_script.sql
```

### 3. 验证导入结果
```sql
-- 检查表数量
SELECT COUNT(*) as TableCount 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE' 
AND TABLE_NAME LIKE 'cb_%'
-- 预期结果: 291

-- 检查是否包含测试表
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE '%bak%' 
   OR TABLE_NAME LIKE '%temp%'
   OR TABLE_NAME LIKE '%tmp%'
   OR TABLE_NAME LIKE '%test%'
-- 预期结果: 无记录

-- 查看所有cb_开头的表
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME LIKE 'cb_%'
ORDER BY TABLE_NAME
```

## 性能优化建议

### 1. 执行前准备
```sql
-- 设置数据库为简单恢复模式（可选，用于提高导入速度）
ALTER DATABASE [qtcc_cb] SET RECOVERY SIMPLE
GO

-- 增加数据库文件大小（避免频繁自动增长）
ALTER DATABASE [qtcc_cb] 
MODIFY FILE (NAME = 'qtcc_cb', SIZE = 1GB, FILEGROWTH = 100MB)
GO
```

### 2. 执行后优化
```sql
-- 更新统计信息
EXEC sp_updatestats
GO

-- 重建索引（如果需要）
EXEC sp_MSforeachtable 'ALTER INDEX ALL ON ? REBUILD'
GO

-- 恢复完整恢复模式（如果之前修改过）
ALTER DATABASE [qtcc_cb] SET RECOVERY FULL
GO
```

## 注意事项

### ⚠️ 重要提醒
1. **备份现有数据**: 在执行脚本前，请备份现有数据库
2. **权限检查**: 确保执行用户具有CREATE TABLE、ALTER等权限
3. **磁盘空间**: 确保有足够的磁盘空间（建议至少2GB可用空间）
4. **依赖关系**: 脚本已处理表间依赖关系，按顺序执行即可

### 🔍 故障排除

**常见问题及解决方案**:

1. **权限不足错误**
   ```
   解决方案: 使用具有db_ddladmin权限的用户执行
   ```

2. **表已存在错误**
   ```sql
   -- 删除现有表（谨慎操作）
   DROP TABLE IF EXISTS [table_name]
   ```

3. **外键约束错误**
   ```sql
   -- 临时禁用外键检查
   EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL'
   -- 执行完成后重新启用
   EXEC sp_MSforeachtable 'ALTER TABLE ? CHECK CONSTRAINT ALL'
   ```

## 文件统计信息

| 项目 | 数值 |
|------|------|
| 总语句数 | 36,383 |
| 过滤后语句数 | 8,487 |
| 包含表数 | 291 |
| 文件行数 | 31,994 |
| 过滤率 | 76.7% |

## 相关文件

- `cb_production_tables_list.txt` - 生产环境表列表
- `test_tables_analysis_report.md` - 测试表分析报告
- `script_processing_report.md` - 原始文件处理报告
- `final_analysis_summary.txt` - 最终分析总结

---

**生成工具**: ProductionDDLGenerator  
**版本**: 1.0  
**最后更新**: 2025-08-25