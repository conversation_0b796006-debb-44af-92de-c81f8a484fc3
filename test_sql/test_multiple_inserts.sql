-- 测试多条INSERT语句的计数器限制功能

-- 创建测试表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='test_table' AND xtype='U')
BEGIN
    CREATE TABLE test_table (
        id INT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(50),
        value INT
    )
END

-- 清空测试表
TRUNCATE TABLE test_table

-- 第1条INSERT语句
INSERT INTO test_table (name, value) VALUES ('Record1', 100)

-- 第2条INSERT语句  
INSERT INTO test_table (name, value) VALUES ('Record2', 200)

-- 第3条INSERT语句
INSERT INTO test_table (name, value) VALUES ('Record3', 300)

-- 第4条INSERT语句
INSERT INTO test_table (name, value) VALUES ('Record4', 400)

-- 第5条INSERT语句
INSERT INTO test_table (name, value) VALUES ('Record5', 500)

-- 查询结果
SELECT COUNT(*) as total_rows FROM test_table
SELECT * FROM test_table ORDER BY id