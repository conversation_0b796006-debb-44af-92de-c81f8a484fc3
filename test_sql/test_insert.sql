-- 测试INSERT...SELECT语句的行数限制功能

-- 创建测试表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='test_table' AND xtype='U')
CREATE TABLE test_table (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name VARCHAR(50),
    value INT
)
GO

-- 清空测试表
TRUNCATE TABLE test_table
GO

-- 插入测试数据（这里会被行数限制功能修改）
INSERT INTO test_table (name, value)
SELECT 
    'Test' + CAST(ROW_NUMBER() OVER (ORDER BY object_id) AS VARCHAR(10)) as name,
    ROW_NUMBER() OVER (ORDER BY object_id) as value
FROM sys.objects
GO

-- 查询结果
SELECT COUNT(*) as total_rows FROM test_table
GO

SELECT TOP 10 * FROM test_table ORDER BY id
GO