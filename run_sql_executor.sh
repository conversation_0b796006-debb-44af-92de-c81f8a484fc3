#!/bin/bash
# SQL批量执行工具启动脚本
# 自动设置ODBC环境变量

# 设置ODBC配置目录
export ODBCSYSINI=/opt/homebrew/etc

# 检查配置文件是否存在
if [ ! -f "config.json" ]; then
    echo "错误: 找不到config.json配置文件"
    exit 1
fi

# 运行SQL批量执行工具
echo "启动SQL批量执行工具..."
echo "ODBC配置目录: $ODBCSYSINI"
echo "当前工作目录: $(pwd)"
echo "========================================"

# 选项1：传递所有参数给Python脚本（当前行为）
python3 new_sql_batch_executor.py "$@"

# 选项2：如果你想强制使用配置文件设置，忽略命令行参数，可以使用下面这行替代上面的行：
# python3 new_sql_batch_executor.py