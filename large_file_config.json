{"database": {"server": "************", "database": "dotnet_erp352SP4", "username": "Hz", "password": "Aa123123", "driver": "ODBC Driver 17 for SQL Server", "timeout": 120, "autocommit": false}, "execution": {"max_file_size_mb": 2000, "clear_tables_before_execution": true, "skip_empty_files": true, "default_sql_folder": "/Users/<USER>/Documents/五、青特项目/成本数据库/cb_data_clean_end", "enable_row_limit": true, "limit_rows_per_table": 100}, "performance": {"batch_size": 1000, "buffer_size": 65536, "commit_interval": 10000, "memory_limit_mb": 2048, "enable_progress_display": true, "enable_memory_monitoring": true, "large_file_threshold_mb": 100, "large_file_batch_size": 2000, "large_file_commit_interval": 20000}, "logging": {"level": "INFO", "log_file": "large_file_sql_execution.log", "console_output": true}, "_optimization_notes": {"large_file_optimizations": "针对大文件(>100MB)的特殊优化配置", "batch_size": "大文件使用2000，小文件使用1000", "buffer_size": "64KB缓冲区，适合大文件流式读取", "commit_interval": "大文件20000，小文件10000，减少网络往返", "timeout": "增加到120秒，适应大文件处理", "memory_limit": "2GB内存限制，适合处理超大文件"}}