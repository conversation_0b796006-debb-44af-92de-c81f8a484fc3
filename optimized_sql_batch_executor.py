#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版SQL批量执行工具
性能优化重点：
1. 流式文件读取，降低内存使用
2. 批量SQL执行，提升数据库性能
3. 内存监控和进度显示
4. 连接池和事务优化
"""

import pyodbc
import json
import os
import logging
import argparse
import psutil
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple, Iterator, Optional
from contextlib import contextmanager
import re
import gc


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = time.time()
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss
        self.peak_memory = self.initial_memory
        self.sql_count = 0
        self.file_count = 0
        
    def update_stats(self, sql_executed: int = 0, files_processed: int = 0):
        """更新统计信息"""
        current_memory = self.process.memory_info().rss
        self.peak_memory = max(self.peak_memory, current_memory)
        self.sql_count += sql_executed
        self.file_count += files_processed
        
    def get_memory_usage_mb(self) -> float:
        """获取当前内存使用量(MB)"""
        return self.process.memory_info().rss / 1024 / 1024
        
    def get_peak_memory_mb(self) -> float:
        """获取峰值内存使用量(MB)"""
        return self.peak_memory / 1024 / 1024
        
    def get_elapsed_time(self) -> float:
        """获取已用时间(秒)"""
        return time.time() - self.start_time
        
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        elapsed = self.get_elapsed_time()
        return {
            'elapsed_time': elapsed,
            'current_memory_mb': self.get_memory_usage_mb(),
            'peak_memory_mb': self.get_peak_memory_mb(),
            'sql_per_second': self.sql_count / elapsed if elapsed > 0 else 0,
            'files_per_second': self.file_count / elapsed if elapsed > 0 else 0,
            'total_sql_executed': self.sql_count,
            'total_files_processed': self.file_count
        }


class StreamingSQLReader:
    """流式SQL文件读取器"""
    
    def __init__(self, file_path: str, buffer_size: int = 8192):
        self.file_path = file_path
        self.buffer_size = buffer_size
        self.current_statement = ""
        
    def read_sql_statements(self) -> Iterator[str]:
        """流式读取SQL语句"""
        try:
            with open(self.file_path, 'r', encoding='utf-8', buffering=self.buffer_size) as f:
                buffer = ""
                
                while True:
                    chunk = f.read(self.buffer_size)
                    if not chunk:
                        # 处理最后的语句
                        if buffer.strip():
                            yield buffer.strip()
                        break
                        
                    buffer += chunk
                    
                    # 检查是否包含GO分隔符
                    if 'GO' in buffer.upper():
                        statements = self._split_by_go(buffer)
                        for stmt in statements[:-1]:  # 除了最后一个
                            if stmt.strip():
                                yield stmt.strip()
                        buffer = statements[-1]  # 保留最后一个未完成的部分
                    elif ';' in buffer:
                        statements = buffer.split(';')
                        for stmt in statements[:-1]:  # 除了最后一个
                            if stmt.strip():
                                yield stmt.strip()
                        buffer = statements[-1]  # 保留最后一个未完成的部分
                        
        except Exception as e:
            raise Exception(f"读取文件失败 {self.file_path}: {e}")
            
    def _split_by_go(self, text: str) -> List[str]:
        """按GO分割SQL语句"""
        # 使用正则表达式匹配独立的GO语句
        pattern = r'\bGO\b'
        return re.split(pattern, text, flags=re.IGNORECASE)


class OptimizedSQLBatchExecutor:
    """优化版SQL批量执行器"""
    
    def __init__(self, config_file: str = 'config.json'):
        """初始化执行器"""
        self.config = self._load_config(config_file)
        self.connection = None
        self.cursor = None
        self.logger = self._setup_logging()
        self.performance_monitor = PerformanceMonitor()
        self.default_sql_folder = self.config.get('execution', {}).get('default_sql_folder', '')
        
        # 性能配置
        self.batch_size = self.config.get('performance', {}).get('batch_size', 100)
        self.buffer_size = self.config.get('performance', {}).get('buffer_size', 8192)
        self.commit_interval = self.config.get('performance', {}).get('commit_interval', 1000)
        self.memory_limit_mb = self.config.get('performance', {}).get('memory_limit_mb', 512)
        
        self.stats = {
            'total_files': 0,
            'success_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'cleared_tables': 0,
            'total_sql_executed': 0
        }
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "database": {
                "server": "localhost",
                "database": "test_db",
                "username": "sa",
                "password": "password",
                "driver": "ODBC Driver 17 for SQL Server",
                "timeout": 30,
                "autocommit": False
            },
            "execution": {
                "max_file_size_mb": 100,
                "clear_tables_before_execution": True,
                "skip_empty_files": True,
                "default_sql_folder": "",
                "enable_row_limit": False,
                "limit_rows_per_table": 100
            },
            "performance": {
                "batch_size": 100,
                "buffer_size": 8192,
                "commit_interval": 1000,
                "memory_limit_mb": 512,
                "enable_progress_display": True,
                "enable_memory_monitoring": True
            },
            "logging": {
                "level": "INFO",
                "log_file": "sql_execution.log",
                "console_output": True
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key in default_config:
                    if key not in config:
                        config[key] = default_config[key]
                    elif isinstance(default_config[key], dict):
                        for subkey in default_config[key]:
                            if subkey not in config[key]:
                                config[key][subkey] = default_config[key][subkey]
                return config
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            print(f"已创建默认配置文件: {config_file}")
            return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('OptimizedSQLBatchExecutor')
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 文件处理器
        log_file = self.config['logging']['log_file']
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        if self.config['logging']['console_output']:
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    @contextmanager
    def database_connection(self):
        """数据库连接上下文管理器"""
        try:
            db_config = self.config['database']
            connection_string = (
                f"DRIVER={{{db_config['driver']}}};"
                f"SERVER={db_config['server']};"
                f"DATABASE={db_config['database']};"
                f"UID={db_config['username']};"
                f"PWD={db_config['password']};"
                "TrustServerCertificate=yes;"
            )
            
            self.connection = pyodbc.connect(
                connection_string,
                timeout=db_config.get('timeout', 30),
                autocommit=db_config.get('autocommit', False)
            )
            self.cursor = self.connection.cursor()
            self.logger.info(f"成功连接到数据库: {db_config['server']}/{db_config['database']}")
            
            yield self.connection
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
        finally:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
                self.logger.info("数据库连接已关闭")
    
    def check_memory_usage(self):
        """检查内存使用情况"""
        if self.config['performance']['enable_memory_monitoring']:
            current_memory = self.performance_monitor.get_memory_usage_mb()
            if current_memory > self.memory_limit_mb:
                self.logger.warning(f"内存使用超过限制: {current_memory:.1f}MB > {self.memory_limit_mb}MB")
                # 强制垃圾回收
                gc.collect()
                new_memory = self.performance_monitor.get_memory_usage_mb()
                self.logger.info(f"垃圾回收后内存使用: {new_memory:.1f}MB")

    def get_sql_files(self, folder_path: str) -> List[str]:
        """获取文件夹下的SQL文件"""
        sql_files = []
        max_size_bytes = self.config['execution']['max_file_size_mb'] * 1024 * 1024

        try:
            folder = Path(folder_path)
            if not folder.exists():
                self.logger.error(f"文件夹不存在: {folder_path}")
                return []

            for file_path in folder.glob('*.sql'):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    if file_size > max_size_bytes:
                        self.logger.warning(f"跳过大文件: {file_path.name} ({file_size/1024/1024:.1f}MB)")
                        self.stats['skipped_files'] += 1
                        continue

                    if self.config['execution']['skip_empty_files'] and file_size == 0:
                        self.logger.warning(f"跳过空文件: {file_path.name}")
                        self.stats['skipped_files'] += 1
                        continue

                    sql_files.append(str(file_path))
                    self.logger.debug(f"找到SQL文件: {file_path.name} ({file_size/1024:.1f}KB)")

            self.stats['total_files'] = len(sql_files)
            self.logger.info(f"找到 {len(sql_files)} 个SQL文件")
            return sorted(sql_files)

        except Exception as e:
            self.logger.error(f"扫描SQL文件时出错: {e}")
            return []

    def extract_table_name(self, sql_content: str) -> Optional[str]:
        """从SQL内容中提取表名"""
        try:
            # 只检查前几行，避免处理整个文件
            lines = sql_content.split('\n')[:10]  # 只检查前10行
            for line in lines:
                line = line.strip().upper()
                if 'INSERT INTO' in line:
                    parts = line.split('INSERT INTO')[1].strip().split()
                    if parts:
                        table_name = parts[0].strip('(').strip()
                        if '.' in table_name:
                            table_name = table_name.split('.')[-1]
                        return table_name
                elif 'CREATE TABLE' in line:
                    parts = line.split('CREATE TABLE')[1].strip().split()
                    if parts:
                        table_name = parts[0].strip('(').strip()
                        if '.' in table_name:
                            table_name = table_name.split('.')[-1]
                        return table_name
            return None
        except Exception:
            return None

    def clear_table_data(self, table_name: str) -> bool:
        """清空表数据"""
        try:
            # 检查表是否存在
            check_sql = f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'"
            self.cursor.execute(check_sql)
            if self.cursor.fetchone()[0] == 0:
                self.logger.warning(f"表不存在，跳过清理: {table_name}")
                return True

            # 清空表数据
            delete_sql = f"DELETE FROM {table_name}"
            self.cursor.execute(delete_sql)
            self.connection.commit()

            self.logger.info(f"已清空表数据: {table_name}")
            self.stats['cleared_tables'] += 1
            return True

        except Exception as e:
            self.logger.error(f"清空表数据失败 {table_name}: {e}")
            return False

    def execute_sql_file_optimized(self, file_path: str) -> bool:
        """优化版执行单个SQL文件"""
        try:
            file_name = os.path.basename(file_path)
            self.logger.info(f"开始执行: {file_name}")

            # 重置INSERT计数器
            insert_counter = 0
            sql_executed = 0

            # 获取行数限制配置
            limit_rows = 0
            if self.config['execution'].get('enable_row_limit', False):
                limit_rows = self.config['execution'].get('limit_rows_per_table', 100)

            # 使用流式读取器
            reader = StreamingSQLReader(file_path, self.buffer_size)

            # 获取第一个SQL语句来提取表名（用于清理）
            first_statement = None
            statements_iterator = reader.read_sql_statements()

            try:
                first_statement = next(statements_iterator)
            except StopIteration:
                self.logger.warning(f"文件为空: {file_name}")
                return True

            # 如果配置了清空表数据
            if self.config['execution']['clear_tables_before_execution']:
                table_name = self.extract_table_name(first_statement)
                if table_name:
                    self.clear_table_data(table_name)

            # 批量执行SQL语句
            batch_statements = [first_statement] if first_statement.strip() else []

            for statement in statements_iterator:
                if not statement.strip():
                    continue

                # 检查行数限制
                if limit_rows > 0 and statement.strip().upper().startswith('INSERT'):
                    insert_counter += 1
                    if insert_counter > limit_rows:
                        self.logger.info(f"已达到INSERT语句限制 ({limit_rows})，结束当前文件处理")
                        break

                batch_statements.append(statement)

                # 批量执行
                if len(batch_statements) >= self.batch_size:
                    executed = self._execute_batch(batch_statements, file_name)
                    sql_executed += executed
                    batch_statements = []

                    # 检查内存使用
                    self.check_memory_usage()

                    # 更新性能监控
                    self.performance_monitor.update_stats(sql_executed=executed)

            # 执行剩余的语句
            if batch_statements:
                executed = self._execute_batch(batch_statements, file_name)
                sql_executed += executed
                self.performance_monitor.update_stats(sql_executed=executed)

            # 最终提交
            self.connection.commit()

            self.logger.info(f"执行成功: {file_name} (共执行 {sql_executed} 条SQL语句)")
            self.stats['success_files'] += 1
            self.stats['total_sql_executed'] += sql_executed

            # 更新文件处理统计
            self.performance_monitor.update_stats(files_processed=1)

            return True

        except Exception as e:
            self.logger.error(f"执行失败 {file_name}: {e}")
            self.stats['failed_files'] += 1
            # 回滚事务
            try:
                self.connection.rollback()
            except:
                pass
            return False

    def _execute_batch(self, statements: List[str], file_name: str) -> int:
        """批量执行SQL语句"""
        executed_count = 0

        try:
            for i, statement in enumerate(statements):
                if statement.strip():
                    try:
                        self.cursor.execute(statement)
                        executed_count += 1

                        # 定期提交
                        if executed_count % self.commit_interval == 0:
                            self.connection.commit()

                    except Exception as stmt_error:
                        self.logger.error(f"执行SQL语句失败 {file_name} (批次中第 {i+1} 条): {stmt_error}")
                        # 继续执行其他语句，不中断整个批次
                        continue

            return executed_count

        except Exception as e:
            self.logger.error(f"批量执行失败 {file_name}: {e}")
            return executed_count

    def execute_folder(self, folder_path: str) -> Dict:
        """执行文件夹下的所有SQL文件"""
        start_time = datetime.now()
        self.logger.info(f"开始批量执行SQL文件，文件夹: {folder_path}")

        try:
            with self.database_connection():
                # 获取SQL文件列表
                sql_files = self.get_sql_files(folder_path)

                if not sql_files:
                    self.logger.warning("未找到SQL文件")
                    return self.stats

                # 显示性能配置
                self.logger.info(f"性能配置 - 批处理大小: {self.batch_size}, "
                               f"缓冲区大小: {self.buffer_size}, "
                               f"提交间隔: {self.commit_interval}")

                # 执行每个SQL文件
                for i, file_path in enumerate(sql_files, 1):
                    # 显示进度
                    if self.config['performance']['enable_progress_display']:
                        progress = (i / len(sql_files)) * 100
                        memory_usage = self.performance_monitor.get_memory_usage_mb()
                        elapsed = self.performance_monitor.get_elapsed_time()

                        print(f"\r进度: {progress:.1f}% ({i}/{len(sql_files)}) | "
                              f"内存: {memory_usage:.1f}MB | "
                              f"用时: {elapsed:.1f}s", end='', flush=True)

                    self.execute_sql_file_optimized(file_path)

                print()  # 换行

                # 计算执行时间
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()

                # 打印执行摘要
                self.print_summary(execution_time)

        except Exception as e:
            self.logger.error(f"批量执行过程中发生错误: {e}")

        return self.stats

    def print_summary(self, execution_time: float):
        """打印执行摘要"""
        total = self.stats['total_files'] + self.stats['skipped_files']
        success_rate = (self.stats['success_files'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0

        # 获取性能报告
        perf_report = self.performance_monitor.get_performance_report()

        summary = f"""
{'='*80}
执行摘要 - 优化版SQL批量执行器
{'='*80}
文件统计:
  总文件数: {total}
  处理文件数: {self.stats['total_files']}
  成功文件数: {self.stats['success_files']}
  失败文件数: {self.stats['failed_files']}
  跳过文件数: {self.stats['skipped_files']}
  清理表数: {self.stats['cleared_tables']}
  成功率: {success_rate:.1f}%

SQL执行统计:
  总SQL语句数: {self.stats['total_sql_executed']}
  平均SQL/秒: {perf_report['sql_per_second']:.1f}
  平均文件/秒: {perf_report['files_per_second']:.2f}

性能指标:
  执行时间: {execution_time:.2f}秒
  当前内存使用: {perf_report['current_memory_mb']:.1f}MB
  峰值内存使用: {perf_report['peak_memory_mb']:.1f}MB
  内存效率: {(perf_report['peak_memory_mb'] - perf_report['current_memory_mb']):.1f}MB已释放

配置参数:
  批处理大小: {self.batch_size}
  缓冲区大小: {self.buffer_size}
  提交间隔: {self.commit_interval}
  内存限制: {self.memory_limit_mb}MB

日志文件: {self.config['logging']['log_file']}
{'='*80}
"""

        print(summary)
        self.logger.info(f"执行完成 - 成功: {self.stats['success_files']}, "
                        f"失败: {self.stats['failed_files']}, "
                        f"跳过: {self.stats['skipped_files']}, "
                        f"SQL执行: {self.stats['total_sql_executed']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化版SQL批量执行工具')
    parser.add_argument('folder', nargs='?', help='包含SQL文件的文件夹路径（可选，未指定时使用配置文件中的默认路径）')
    parser.add_argument('-c', '--config', default='config.json', help='配置文件路径 (默认: config.json)')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--limit-rows', type=int, help='每张表限制导入的行数，覆盖配置文件设置')
    parser.add_argument('--no-limit', action='store_true', help='禁用行数限制功能')
    parser.add_argument('--batch-size', type=int, help='批处理大小，覆盖配置文件设置')
    parser.add_argument('--memory-limit', type=int, help='内存限制(MB)，覆盖配置文件设置')

    args = parser.parse_args()

    # 创建执行器实例
    executor = OptimizedSQLBatchExecutor(args.config)

    # 应用命令行参数覆盖配置
    if args.limit_rows:
        executor.config['execution']['enable_row_limit'] = True
        executor.config['execution']['limit_rows_per_table'] = args.limit_rows

    if args.no_limit:
        executor.config['execution']['enable_row_limit'] = False

    if args.batch_size:
        executor.batch_size = args.batch_size
        executor.config['performance']['batch_size'] = args.batch_size

    if args.memory_limit:
        executor.memory_limit_mb = args.memory_limit
        executor.config['performance']['memory_limit_mb'] = args.memory_limit

    # 确定要使用的文件夹路径
    folder_path = args.folder
    if not folder_path:
        folder_path = executor.default_sql_folder
        if not folder_path:
            print("错误: 未指定文件夹路径，且配置文件中未设置默认路径")
            print("请在命令行指定文件夹路径，或在配置文件中设置 execution.default_sql_folder")
            return 1
        print(f"使用配置文件中的默认路径: {folder_path}")

    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在 - {folder_path}")
        return 1

    try:
        # 如果启用详细输出，设置日志级别为DEBUG
        if args.verbose:
            executor.logger.setLevel(logging.DEBUG)

        # 执行SQL文件
        stats = executor.execute_folder(folder_path)

        # 返回适当的退出码
        if stats['failed_files'] > 0:
            return 1
        return 0

    except KeyboardInterrupt:
        print("\n用户中断执行")
        return 130
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
