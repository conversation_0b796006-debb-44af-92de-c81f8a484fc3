#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL批量执行工具
功能：批量执行指定文件夹下的SQL文件
支持：配置文件、日志记录、表数据清理
"""

import pyodbc
import json
import os
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Tuple


class SQLBatchExecutor:
    """SQL批量执行器"""
    
    def __init__(self, config_file: str = 'config.json'):
        """初始化执行器"""
        self.config = self._load_config(config_file)
        self.connection = None
        self.logger = self._setup_logging()
        self.default_sql_folder = self.config.get('execution', {}).get('default_sql_folder', '')
        self.stats = {
            'total_files': 0,
            'success_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'cleared_tables': 0
        }
    
    def _load_config(self, config_file: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "database": {
                "server": "localhost",
                "database": "test_db",
                "username": "sa",
                "password": "password",
                "driver": "ODBC Driver 17 for SQL Server"
            },
            "execution": {
                "max_file_size_mb": 100,
                "clear_tables_before_execution": True,
                "skip_empty_files": True,
                "default_sql_folder": ""
            },
            "logging": {
                "level": "INFO",
                "log_file": "sql_execution.log",
                "console_output": True
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                for key in default_config:
                    if key not in config:
                        config[key] = default_config[key]
                    elif isinstance(default_config[key], dict):
                        for subkey in default_config[key]:
                            if subkey not in config[key]:
                                config[key][subkey] = default_config[key][subkey]
                return config
            except Exception as e:
                print(f"配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)
            print(f"已创建默认配置文件: {config_file}")
            return default_config
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('SQLBatchExecutor')
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 文件处理器
        log_file = self.config['logging']['log_file']
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        if self.config['logging']['console_output']:
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            db_config = self.config['database']
            connection_string = (
                f"DRIVER={{{db_config['driver']}}};"
                f"SERVER={db_config['server']};"
                f"DATABASE={db_config['database']};"
                f"UID={db_config['username']};"
                f"PWD={db_config['password']};"
                "TrustServerCertificate=yes;"
            )
            
            self.connection = pyodbc.connect(connection_string)
            self.logger.info(f"成功连接到数据库: {db_config['server']}/{db_config['database']}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect_database(self):
        """断开数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                self.logger.info("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接时出错: {e}")
    
    def get_sql_files(self, folder_path: str) -> List[str]:
        """获取文件夹下的SQL文件"""
        sql_files = []
        max_size_bytes = self.config['execution']['max_file_size_mb'] * 1024 * 1024
        
        try:
            folder = Path(folder_path)
            if not folder.exists():
                self.logger.error(f"文件夹不存在: {folder_path}")
                return []
            
            for file_path in folder.glob('*.sql'):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    if file_size > max_size_bytes:
                        self.logger.warning(f"跳过大文件: {file_path.name} ({file_size/1024/1024:.1f}MB)")
                        self.stats['skipped_files'] += 1
                        continue
                    
                    if self.config['execution']['skip_empty_files'] and file_size == 0:
                        self.logger.warning(f"跳过空文件: {file_path.name}")
                        self.stats['skipped_files'] += 1
                        continue
                    
                    sql_files.append(str(file_path))
                    self.logger.debug(f"找到SQL文件: {file_path.name} ({file_size/1024:.1f}KB)")
            
            self.stats['total_files'] = len(sql_files)
            self.logger.info(f"找到 {len(sql_files)} 个SQL文件")
            return sorted(sql_files)
            
        except Exception as e:
            self.logger.error(f"扫描SQL文件时出错: {e}")
            return []
    
    def extract_table_name(self, sql_content: str) -> str:
        """从SQL内容中提取表名"""
        try:
            # 简单的表名提取逻辑，可根据需要改进
            lines = sql_content.upper().split('\n')
            for line in lines:
                line = line.strip()
                if 'INSERT INTO' in line:
                    parts = line.split('INSERT INTO')[1].strip().split()
                    if parts:
                        table_name = parts[0].strip('(').strip()
                        # 移除schema前缀
                        if '.' in table_name:
                            table_name = table_name.split('.')[-1]
                        return table_name
                elif 'CREATE TABLE' in line:
                    parts = line.split('CREATE TABLE')[1].strip().split()
                    if parts:
                        table_name = parts[0].strip('(').strip()
                        if '.' in table_name:
                            table_name = table_name.split('.')[-1]
                        return table_name
            return None
        except Exception:
            return None
    
    def clear_table_data(self, table_name: str) -> bool:
        """清空表数据"""
        try:
            cursor = self.connection.cursor()
            
            # 检查表是否存在
            check_sql = f"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{table_name}'"
            cursor.execute(check_sql)
            if cursor.fetchone()[0] == 0:
                self.logger.warning(f"表不存在，跳过清理: {table_name}")
                return True
            
            # 清空表数据
            delete_sql = f"DELETE FROM {table_name}"
            cursor.execute(delete_sql)
            self.connection.commit()
            
            self.logger.info(f"已清空表数据: {table_name}")
            self.stats['cleared_tables'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"清空表数据失败 {table_name}: {e}")
            return False
    
    def should_execute_insert(self, sql_stmt: str, limit_rows: int = 0) -> bool:
        """检查是否应该执行这条INSERT语句（基于计数器限制）"""
        # 如果没有设置限制，执行所有语句
        if limit_rows <= 0:
            return True
        
        # 移除注释和空行，获取实际的SQL语句
        lines = sql_stmt.split('\n')
        clean_lines = []
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('--'):
                clean_lines.append(stripped)
        
        if not clean_lines:
            return True
        
        first_line = clean_lines[0].upper()
        
        # 检查是否为INSERT语句
        if first_line.startswith('INSERT'):
            # 初始化INSERT计数器
            if not hasattr(self, 'insert_counter'):
                self.insert_counter = 0
            
            self.insert_counter += 1
            
            if self.insert_counter <= limit_rows:
                self.logger.debug(f"执行第 {self.insert_counter} 条INSERT语句（限制: {limit_rows}）")
                return True
            else:
                self.logger.info(f"已达到INSERT语句限制 ({limit_rows})，跳过第 {self.insert_counter} 条INSERT语句")
                return False
        
        return True
    

    
    def execute_sql_file(self, file_path: str) -> bool:
        """执行单个SQL文件"""
        try:
            file_name = os.path.basename(file_path)
            self.logger.info(f"开始执行: {file_name}")
            
            # 重置INSERT计数器（每个文件独立计数）
            self.insert_counter = 0
            
            # 读取SQL文件
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read().strip()
            
            if not sql_content:
                self.logger.warning(f"文件为空: {file_name}")
                return True
            
            # 如果配置了清空表数据
            if self.config['execution']['clear_tables_before_execution']:
                table_name = self.extract_table_name(sql_content)
                if table_name:
                    self.clear_table_data(table_name)
            
            # 获取行数限制配置
            limit_rows = 0
            if self.config['execution'].get('enable_row_limit', False):
                limit_rows = self.config['execution'].get('limit_rows_per_table', 100)
            
            # 执行SQL
            cursor = self.connection.cursor()
            
            # 分割SQL语句（以GO或分号分割）
            sql_statements = []
            if 'GO' in sql_content.upper():
                sql_statements = [stmt.strip() for stmt in sql_content.split('GO') if stmt.strip()]
            else:
                sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            if not sql_statements:
                sql_statements = [sql_content]
            
            # 执行每个SQL语句
            for i, sql_stmt in enumerate(sql_statements):
                if sql_stmt.strip():
                    try:
                        # 调试：显示当前处理的SQL语句开头
                        self.logger.debug(f"处理SQL语句 {i+1}: {sql_stmt.strip()[:50]}...")
                        
                        # 检查是否应该执行这条INSERT语句
                        if not self.should_execute_insert(sql_stmt, limit_rows):
                            # 如果是INSERT语句且达到限制，直接结束当前文件处理
                            if limit_rows > 0 and hasattr(self, 'insert_counter') and self.insert_counter > limit_rows:
                                self.logger.info(f"已达到INSERT语句限制 ({limit_rows})，结束当前文件处理")
                                break
                            self.logger.debug(f"跳过SQL语句 {i+1}（已达到INSERT限制）")
                            continue
                        
                        cursor.execute(sql_stmt)
                        self.connection.commit()
                    except Exception as stmt_error:
                        self.logger.error(f"执行SQL语句失败 {file_name} (语句 {i+1}): {stmt_error}")
                        raise stmt_error
            
            self.logger.info(f"执行成功: {file_name}")
            self.stats['success_files'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"执行失败 {file_name}: {e}")
            self.stats['failed_files'] += 1
            return False
    
    def execute_folder(self, folder_path: str) -> Dict:
        """执行文件夹下的所有SQL文件"""
        start_time = datetime.now()
        self.logger.info(f"开始批量执行SQL文件，文件夹: {folder_path}")
        
        # 初始化INSERT计数器
        self.insert_counter = 0
        
        # 连接数据库
        if not self.connect_database():
            return self.stats
        
        try:
            # 获取SQL文件列表
            sql_files = self.get_sql_files(folder_path)
            
            if not sql_files:
                self.logger.warning("未找到SQL文件")
                return self.stats
            
            # 执行每个SQL文件
            for file_path in sql_files:
                self.execute_sql_file(file_path)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 打印执行摘要
            self.print_summary(execution_time)
            
        finally:
            self.disconnect_database()
        
        return self.stats
    

    
    def print_summary(self, execution_time: float):
        """打印执行摘要"""
        total = self.stats['total_files'] + self.stats['skipped_files']
        success_rate = (self.stats['success_files'] / self.stats['total_files'] * 100) if self.stats['total_files'] > 0 else 0
        
        summary = f"""
{'='*60}
执行摘要
{'='*60}
总文件数: {total}
处理文件数: {self.stats['total_files']}
成功文件数: {self.stats['success_files']}
失败文件数: {self.stats['failed_files']}
跳过文件数: {self.stats['skipped_files']}
清理表数: {self.stats['cleared_tables']}
成功率: {success_rate:.1f}%
执行时间: {execution_time:.2f}秒
日志文件: {self.config['logging']['log_file']}
{'='*60}
"""
        
        print(summary)
        self.logger.info(f"执行完成 - 成功: {self.stats['success_files']}, 失败: {self.stats['failed_files']}, 跳过: {self.stats['skipped_files']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SQL批量执行工具')
    parser.add_argument('folder', nargs='?', help='包含SQL文件的文件夹路径（可选，未指定时使用配置文件中的默认路径）')
    parser.add_argument('-c', '--config', default='config.json', help='配置文件路径 (默认: config.json)')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--limit-rows', type=int, help='每张表限制导入的行数，覆盖配置文件设置')
    parser.add_argument('--no-limit', action='store_true', help='禁用行数限制功能')
    
    args = parser.parse_args()
    
    # 创建执行器实例以获取默认路径
    executor = SQLBatchExecutor(args.config)
    
    # 应用命令行参数覆盖配置
    if args.limit_rows:
        executor.config['execution']['enable_row_limit'] = True
        executor.config['execution']['limit_rows_per_table'] = args.limit_rows
    
    if args.no_limit:
        executor.config['execution']['enable_row_limit'] = False
    
    # 确定要使用的文件夹路径
    folder_path = args.folder
    if not folder_path:
        folder_path = executor.default_sql_folder
        if not folder_path:
            print("错误: 未指定文件夹路径，且配置文件中未设置默认路径")
            print("请在命令行指定文件夹路径，或在配置文件中设置 execution.default_sql_folder")
            return 1
        print(f"使用配置文件中的默认路径: {folder_path}")
    
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 文件夹不存在 - {folder_path}")
        return 1
    
    try:
        # 如果启用详细输出，设置日志级别为DEBUG
        if args.verbose:
            executor.logger.setLevel(logging.DEBUG)
        
        # 执行SQL文件
        stats = executor.execute_folder(folder_path)
        
        # 返回适当的退出码
        if stats['failed_files'] > 0:
            return 1
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
        return 130
    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())