# SQL文件测试表分析报告

## 分析概述

本报告分析了从 `script.sql` 文件中过滤出的416个 `cb_` 开头的表，识别其中可能为测试用途的表。

## 分析结果

### 1. 测试表分类统计

| 类别 | 数量 | 占比 | 说明 |
|------|------|------|------|
| 明确备份表 | 5 | 1.2% | 包含 'bak' 关键词的表 |
| 历史版本表 | 121 | 29.1% | 带日期后缀的表 |
| 模板表 | 8 | 1.9% | 包含 'template'/'tmpl' 的表 |
| **疑似测试表总计** | **134** | **32.2%** | 可能需要排除的表 |
| 正常业务表 | 282 | 67.8% | 建议保留的表 |

### 2. 明确备份表列表（5个）

```
cb_BudgetBill20220707bak
cb_HTAlter20200326bak
cb_YgAlter2Budget_shenxbak20250612
cb_contract20200327bak
cb_contract20220719bak
```

### 3. 历史版本表特征

历史版本表主要包含以下模式：
- **日期后缀格式**：YYYYMMDD 或 YYYYMM 或 YYYY
- **主要表系列**：
  - `cb_Budget` 系列：约30个历史版本
  - `cb_HTSchedule` 系列：约60个历史版本
  - `cb_Contract` 系列：约15个历史版本
  - `cb_HTFKApply` 系列：约10个历史版本
  - 其他系列：约6个历史版本

### 4. 模板表列表（8个）

```
cb_ContractPlanTemplate
cb_GclTemplet
cb_ListTemplate
cb_XjlTemplate
cb_XjlTemplateDtl
cb_cdb_CommonIndexTmpl
cb_cdb_CupIndexSpecialTypeTmpl
cb_cdb_CupIndexTmpl
cb_sjkBelongAreaTemplet
cb_sjkGhIndexTemplet
```

## 风险评估

### 高风险表（建议排除）
1. **明确备份表**：5个表明确标识为备份，应该排除
2. **大量历史版本表**：121个带日期的表可能包含过时数据

### 中等风险表（需要确认）
1. **模板表**：8个模板表可能是系统必需的配置表，需要业务确认

## 建议措施

### 1. 立即排除的表
- 所有包含 'bak' 的备份表（5个）
- 明显的历史数据表（建议排除日期在6个月前的表）

### 2. 需要业务确认的表
- 模板表：确认是否为系统运行必需
- 近期历史表：确认是否包含有效业务数据

### 3. 优化建议
1. **创建生产环境过滤规则**：
   ```
   排除规则：
   - 表名包含 'bak'
   - 表名以日期结尾且日期早于指定时间
   - 明确标识的测试表
   ```

2. **分阶段导入策略**：
   - 第一阶段：导入282个正常业务表
   - 第二阶段：根据业务需要选择性导入模板表
   - 第三阶段：根据数据需求导入必要的历史表

## 结论

处理后的SQL文件中确实包含大量可能的测试表（32.2%），主要是历史版本表和备份表。建议在生产环境导入前进行进一步筛选，以确保数据的整洁性和系统性能。

---

**分析时间**：2025-08-25  
**分析文件**：script.sql (过滤后)  
**总表数**：416  
**疑似测试表**：134  
**建议保留表**：282