{"database": {"server": "************", "database": "dotnet_erp352SP4", "username": "Hz", "password": "Aa123123", "driver": "ODBC Driver 17 for SQL Server", "timeout": 60, "autocommit": false}, "execution": {"max_file_size_mb": 1500, "clear_tables_before_execution": true, "skip_empty_files": true, "default_sql_folder": "/Users/<USER>/Documents/五、青特项目/成本数据库/cb_data_clean_end", "enable_row_limit": true, "limit_rows_per_table": 100}, "performance": {"batch_size": 500, "buffer_size": 16384, "commit_interval": 2000, "memory_limit_mb": 1024, "enable_progress_display": true, "enable_memory_monitoring": true}, "logging": {"level": "INFO", "log_file": "optimized_sql_execution.log", "console_output": true}, "_performance_notes": {"batch_size": "批处理大小，建议100-1000，根据SQL复杂度调整", "buffer_size": "文件读取缓冲区大小(字节)，建议8192-65536", "commit_interval": "提交间隔，每执行N条SQL语句提交一次事务", "memory_limit_mb": "内存使用限制(MB)，超过时触发垃圾回收", "enable_progress_display": "是否显示实时进度", "enable_memory_monitoring": "是否启用内存监控"}, "_optimization_tips": {"large_files": "处理大文件时，建议增大buffer_size和batch_size", "many_small_files": "处理大量小文件时，建议减小batch_size，增大commit_interval", "memory_constrained": "内存受限环境下，建议减小batch_size和buffer_size", "network_latency": "网络延迟高时，建议增大batch_size和commit_interval"}}