#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Large SQL File INSERT Statement Extractor
Efficiently extracts the first 100 INSERT statements from large SQL files
"""

import os
import re
import logging
import time
from pathlib import Path
from typing import List, Tuple, Optional
from datetime import datetime


class InsertStatementExtractor:
    """Efficiently extracts INSERT statements from large SQL files using streaming"""
    
    def __init__(self, input_dir: str, output_dir: str = "smallsql", buffer_size: int = 8192):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.buffer_size = buffer_size
        self.logger = self._setup_logging()
        
        # Ensure output directory exists
        self.output_dir.mkdir(exist_ok=True)
        
        # Statistics
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'total_inserts_extracted': 0
        }
        
        # INSERT statement detection pattern (case-insensitive)
        self.insert_pattern = re.compile(r'^\s*INSERT\s+INTO\s+', re.IGNORECASE)
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger('InsertExtractor')
        logger.setLevel(logging.INFO)
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # File handler
        log_file = f"insert_extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _detect_insert_start(self, line: str) -> bool:
        """Detect if a line starts an INSERT statement"""
        return bool(self.insert_pattern.match(line.strip()))
    
    def _is_statement_complete(self, statement_lines: List[str]) -> bool:
        """
        Check if the current INSERT statement is complete
        A statement is complete when it ends with a semicolon or GO
        """
        if not statement_lines:
            return False
            
        # Join all lines and check the end
        full_statement = ' '.join(line.strip() for line in statement_lines)
        
        # Remove trailing whitespace and check for terminators
        full_statement = full_statement.rstrip()
        
        # Check for semicolon terminator
        if full_statement.endswith(';'):
            return True
            
        # Check for GO terminator (case-insensitive)
        if re.search(r'\bGO\s*$', full_statement, re.IGNORECASE):
            return True
            
        return False
    
    def _extract_inserts_from_file(self, file_path: Path, max_inserts: int = 100) -> Tuple[List[str], int]:
        """
        Extract INSERT statements from a single file using streaming
        Returns: (list_of_insert_statements, total_inserts_found)
        """
        insert_statements = []
        current_statement_lines = []
        in_insert_statement = False
        inserts_found = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8', buffering=self.buffer_size) as f:
                for line_num, line in enumerate(f, 1):
                    # Skip empty lines when not in a statement
                    if not in_insert_statement and not line.strip():
                        continue
                    
                    # Check if this line starts a new INSERT statement
                    if not in_insert_statement and self._detect_insert_start(line):
                        in_insert_statement = True
                        current_statement_lines = [line.rstrip()]
                        continue
                    
                    # If we're in an INSERT statement, collect lines
                    if in_insert_statement:
                        current_statement_lines.append(line.rstrip())
                        
                        # Check if the statement is complete
                        if self._is_statement_complete(current_statement_lines):
                            # Statement is complete, add it to our collection
                            complete_statement = '\n'.join(current_statement_lines)
                            insert_statements.append(complete_statement)
                            inserts_found += 1
                            
                            # Reset for next statement
                            in_insert_statement = False
                            current_statement_lines = []
                            
                            # Check if we've reached our limit
                            if inserts_found >= max_inserts:
                                self.logger.info(f"Reached limit of {max_inserts} INSERT statements in {file_path.name}")
                                break
                    
                    # If we encounter a new INSERT while already in one, 
                    # treat the previous as incomplete and start fresh
                    elif self._detect_insert_start(line):
                        if current_statement_lines:
                            self.logger.warning(f"Incomplete INSERT statement found at line {line_num} in {file_path.name}")
                        
                        in_insert_statement = True
                        current_statement_lines = [line.rstrip()]
                
                # Handle case where file ends with an incomplete INSERT
                if in_insert_statement and current_statement_lines:
                    # Try to salvage the incomplete statement if it looks reasonable
                    incomplete_statement = '\n'.join(current_statement_lines)
                    if len(incomplete_statement.strip()) > 20:  # Arbitrary minimum length
                        insert_statements.append(incomplete_statement + ';')  # Add semicolon
                        inserts_found += 1
                        self.logger.warning(f"Added incomplete INSERT statement from end of {file_path.name}")
                        
        except UnicodeDecodeError as e:
            self.logger.error(f"Encoding error reading {file_path.name}: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error processing {file_path.name}: {e}")
            raise
            
        return insert_statements, inserts_found
    
    def _write_extracted_statements(self, statements: List[str], output_file: Path, original_file: Path):
        """Write extracted INSERT statements to output file"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Write header comment
                f.write(f"-- Extracted INSERT statements from: {original_file.name}\n")
                f.write(f"-- Extraction date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"-- Total INSERT statements extracted: {len(statements)}\n")
                f.write(f"-- Original file size: {original_file.stat().st_size / 1024 / 1024:.1f} MB\n")
                f.write("-- This file contains only the first 100 INSERT statements from the original\n")
                f.write("\n")
                
                # Write each INSERT statement
                for i, statement in enumerate(statements, 1):
                    f.write(f"-- INSERT Statement {i}\n")
                    f.write(statement)
                    f.write("\n\n")
                    
        except Exception as e:
            self.logger.error(f"Error writing to {output_file}: {e}")
            raise
    
    def process_single_file(self, file_path: Path) -> bool:
        """Process a single SQL file and extract INSERT statements"""
        try:
            file_size_mb = file_path.stat().st_size / 1024 / 1024
            self.logger.info(f"Processing: {file_path.name} ({file_size_mb:.1f} MB)")
            
            start_time = time.time()
            
            # Extract INSERT statements
            statements, total_found = self._extract_inserts_from_file(file_path, max_inserts=100)
            
            if not statements:
                self.logger.warning(f"No INSERT statements found in {file_path.name}")
                return True  # Not a failure, just no INSERTs
            
            # Create output file path
            output_file = self.output_dir / file_path.name
            
            # Write extracted statements
            self._write_extracted_statements(statements, output_file, file_path)
            
            elapsed_time = time.time() - start_time
            extracted_count = len(statements)
            
            self.logger.info(
                f"✓ Completed {file_path.name}: "
                f"extracted {extracted_count} INSERT statements "
                f"(found {total_found} total) in {elapsed_time:.2f}s"
            )
            
            # Update statistics
            self.stats['total_inserts_extracted'] += extracted_count
            
            return True
            
        except Exception as e:
            self.logger.error(f"✗ Failed to process {file_path.name}: {e}")
            return False
    
    def process_all_files(self):
        """Process all SQL files in the input directory"""
        start_time = time.time()
        
        # Find all SQL files
        sql_files = list(self.input_dir.glob('*.sql'))
        self.stats['total_files'] = len(sql_files)
        
        if not sql_files:
            self.logger.warning(f"No SQL files found in {self.input_dir}")
            return
        
        self.logger.info(f"Found {len(sql_files)} SQL files to process")
        self.logger.info(f"Output directory: {self.output_dir.absolute()}")
        
        # Process each file
        for i, file_path in enumerate(sql_files, 1):
            self.logger.info(f"Progress: {i}/{len(sql_files)} ({i/len(sql_files)*100:.1f}%)")
            
            try:
                if self.process_single_file(file_path):
                    self.stats['processed_files'] += 1
                else:
                    self.stats['failed_files'] += 1
                    
            except KeyboardInterrupt:
                self.logger.info("Process interrupted by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error processing {file_path.name}: {e}")
                self.stats['failed_files'] += 1
                continue
        
        # Print final statistics
        total_time = time.time() - start_time
        self.print_summary(total_time)
    
    def print_summary(self, total_time: float):
        """Print processing summary"""
        summary = f"""
{'='*80}
INSERT Statement Extraction Summary
{'='*80}
Input Directory: {self.input_dir.absolute()}
Output Directory: {self.output_dir.absolute()}

File Statistics:
  Total SQL files found: {self.stats['total_files']}
  Successfully processed: {self.stats['processed_files']}
  Failed to process: {self.stats['failed_files']}
  Success rate: {(self.stats['processed_files']/self.stats['total_files']*100) if self.stats['total_files'] > 0 else 0:.1f}%

Extraction Results:
  Total INSERT statements extracted: {self.stats['total_inserts_extracted']}
  Average per file: {(self.stats['total_inserts_extracted']/self.stats['processed_files']) if self.stats['processed_files'] > 0 else 0:.1f}

Performance:
  Total processing time: {total_time:.2f} seconds
  Average time per file: {(total_time/self.stats['processed_files']) if self.stats['processed_files'] > 0 else 0:.2f} seconds
  Files per second: {(self.stats['processed_files']/total_time) if total_time > 0 else 0:.2f}

Output files are ready for use with your SQL batch executor!
{'='*80}
"""
        print(summary)
        self.logger.info("Processing completed successfully")


def main():
    """Main function"""
    input_directory = "/Users/<USER>/Documents/五、青特项目/成本数据库/cb_data_clean_end"
    output_directory = "smallsql"
    
    # Check if input directory exists
    if not os.path.exists(input_directory):
        print(f"Error: Input directory does not exist: {input_directory}")
        return 1
    
    print(f"Starting INSERT statement extraction...")
    print(f"Input: {input_directory}")
    print(f"Output: {output_directory}")
    print()
    
    try:
        # Create extractor and process files
        extractor = InsertStatementExtractor(input_directory, output_directory)
        extractor.process_all_files()
        return 0
        
    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
        return 130
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
