#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境DDL生成器
基于表列表生成过滤后的DDL文件
"""

import re
from datetime import datetime
from typing import List, Dict, Set

class ProductionDDLGenerator:
    def __init__(self):
        self.statements = []
        self.target_tables = set()
        self.filtered_objects = []
        
    def load_table_list(self, table_list_file: str) -> Set[str]:
        """加载目标表列表"""
        tables = set()
        try:
            with open(table_list_file, 'r', encoding='utf-8') as f:
                for line in f:
                    table_name = line.strip()
                    if table_name:
                        tables.add(table_name.lower())
            print(f"成功加载 {len(tables)} 个目标表")
            return tables
        except FileNotFoundError:
            print(f"表列表文件 {table_list_file} 不存在")
            return set()
    
    def parse_ddl_file(self, file_path: str) -> List[str]:
        """解析DDL文件，按GO分隔符分割"""
        content = ""
        encodings = ['utf-16', 'utf-16-le', 'utf-8', 'gbk', 'latin-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if not content:
            raise Exception("无法读取文件，尝试了所有编码格式")
        
        # 按GO分隔符分割
        statements = re.split(r'\nGO\s*\n', content, flags=re.IGNORECASE)
        statements = [stmt.strip() for stmt in statements if stmt.strip()]
        
        print(f"解析完成: 共找到 {len(statements)} 个语句块")
        return statements
    
    def extract_table_name(self, statement: str) -> str:
        """从语句中提取表名"""
        # 匹配CREATE TABLE语句
        create_table_match = re.search(r'CREATE\s+TABLE\s+(?:\[dbo\]\.)?\[?([^\]\s]+)\]?', statement, re.IGNORECASE)
        if create_table_match:
            return create_table_match.group(1).lower()
        
        # 匹配ALTER TABLE语句
        alter_table_match = re.search(r'ALTER\s+TABLE\s+(?:\[dbo\]\.)?\[?([^\]\s]+)\]?', statement, re.IGNORECASE)
        if alter_table_match:
            return alter_table_match.group(1).lower()
        
        # 匹配外键约束中的表名
        fk_match = re.search(r'REFERENCES\s+(?:\[dbo\]\.)?\[?([^\]\s]+)\]?', statement, re.IGNORECASE)
        if fk_match:
            return fk_match.group(1).lower()
        
        return None
    
    def should_include_statement(self, statement: str) -> bool:
        """判断是否应该包含该语句"""
        # 跳过注释和空语句
        if not statement.strip() or statement.strip().startswith('/*') or statement.strip().startswith('--'):
            return False
        
        # 包含USE、SET等数据库设置语句
        if re.match(r'^\s*(USE|SET|IF)', statement, re.IGNORECASE):
            return True
        
        # 提取表名并检查是否在目标列表中
        table_name = self.extract_table_name(statement)
        if table_name:
            return table_name in self.target_tables
        
        # 对于其他语句（如索引、约束等），如果包含目标表名则包含
        for table in self.target_tables:
            if f'[{table}]' in statement.lower() or f'{table}' in statement.lower():
                return True
        
        return False
    
    def generate_filtered_ddl(self, input_file: str, output_file: str, table_list_file: str) -> str:
        """生成过滤后的DDL文件"""
        # 加载目标表列表
        self.target_tables = self.load_table_list(table_list_file)
        if not self.target_tables:
            print("没有加载到目标表，退出处理")
            return
        
        # 解析DDL文件
        print(f"正在解析DDL文件: {input_file}")
        statements = self.parse_ddl_file(input_file)
        
        # 过滤语句
        filtered_statements = []
        included_tables = set()
        
        for statement in statements:
            if self.should_include_statement(statement):
                filtered_statements.append(statement)
                table_name = self.extract_table_name(statement)
                if table_name and table_name in self.target_tables:
                    included_tables.add(table_name)
        
        # 生成输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入文件头
            f.write(f"-- 生产环境DDL文件\n")
            f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- 输入文件: {input_file}\n")
            f.write(f"-- 表列表文件: {table_list_file}\n")
            f.write(f"-- 目标表数量: {len(self.target_tables)}\n")
            f.write(f"-- 实际包含表数量: {len(included_tables)}\n")
            f.write(f"-- 过滤后语句数量: {len(filtered_statements)}\n")
            f.write("\n" + "="*80 + "\n\n")
            
            # 写入过滤后的语句
            for i, statement in enumerate(filtered_statements):
                if i > 0:
                    f.write("\nGO\n\n")
                f.write(statement)
            
            # 写入文件尾
            f.write("\n\n" + "="*80 + "\n")
            f.write(f"-- 处理完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"-- 总语句数: {len(statements)}\n")
            f.write(f"-- 过滤后语句数: {len(filtered_statements)}\n")
            f.write(f"-- 包含表数: {len(included_tables)}\n")
        
        print(f"\n处理完成！")
        print(f"输入文件: {input_file}")
        print(f"输出文件: {output_file}")
        print(f"目标表数量: {len(self.target_tables)}")
        print(f"实际包含表数量: {len(included_tables)}")
        print(f"过滤后语句数量: {len(filtered_statements)}")
        
        # 显示包含的表列表
        if included_tables:
            print(f"\n包含的表列表 (前20个):")
            for i, table in enumerate(sorted(included_tables)):
                if i < 20:
                    print(f"  - {table}")
                elif i == 20:
                    print(f"  ... 还有 {len(included_tables) - 20} 个表")
                    break
        
        return output_file

def main():
    import sys
    
    if len(sys.argv) != 4:
        print("使用方法: python3 generate_production_ddl.py <输入DDL文件> <输出DDL文件> <表列表文件>")
        print("示例: python3 generate_production_ddl.py script.sql production_ddl.sql cb_production_tables_list.txt")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    table_list_file = sys.argv[3]
    
    generator = ProductionDDLGenerator()
    generator.generate_filtered_ddl(input_file, output_file, table_list_file)

if __name__ == "__main__":
    main()