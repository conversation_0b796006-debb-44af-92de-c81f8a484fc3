-- Extracted INSERT statements from: cb_CodeFormatTemplet_202508231458.sql
-- Extraction date: 2025-08-25 17:32:50
-- Total INSERT statements extracted: 9
-- Original file size: 0.0 MB
-- This file contains only the first 100 INSERT statements from the original

-- INSERT Statement 1
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'合同编码',4,N'项目代码',2,1,1,N'-',N'SJHC.01',NULL),
	 (N'合同编码',9,N'项目名称',1,0,0,N'-',N'四季花城一期',NULL),
	 (N'合同编码',1,N'公司代码',1,1,1,N'-',N'SZ',NULL),
	 (N'合同编码',10,N'公司名称',1,0,0,N'-',N'深圳公司',NULL),
	 (N'合同编码',11,N'部门代码',1,0,0,N'-',N'GCB',NULL),
	 (N'合同编码',8,N'部门名称',1,0,0,N'-',N'工程部',NULL),
	 (N'合同编码',12,N'合同类别代码',1,0,0,N'-',N'001',NULL),
	 (N'非合同编码',4,N'项目代码',2,1,1,N'-',N'SJHC.01',NULL),
	 (N'非合同编码',10,N'项目名称',1,1,0,N'-',N'四季花城一期',NULL),
	 (N'非合同编码',1,N'公司代码',1,1,1,N'-',N'SZ',NULL);

-- INSERT Statement 2
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'非合同编码',11,N'公司名称',1,0,0,N'-',N'深圳公司',NULL),
	 (N'非合同编码',12,N'部门代码',1,0,0,N'-',N'GCB',NULL),
	 (N'合同变更编码',1,N'合同编码',1,0,0,N'-',N'SJHC.01-2010-8-0001',NULL),
	 (N'合同变更编码',2,N'项目代码',2,1,1,N'-',N'SJHC.01',NULL),
	 (N'合同变更编码',3,N'项目名称',1,0,0,N'-',N'四季花城一期',NULL),
	 (N'合同变更编码',4,N'公司代码',1,0,0,N'-',N'SZ',NULL),
	 (N'合同变更编码',5,N'公司名称',1,0,0,N'-',N'深圳公司',NULL),
	 (N'材料编码',1,N'材料类别代码',1,1,1,N'-',N'04.01.01.01',NULL),
	 (N'材料编码',2,N'流水号',4,1,0,N'',N'00001',NULL),
	 (N'材料编码',1,N'材料类别名称',1,0,0,N'-',N'材料设备采购-材料采购-装修材料-涂料类',NULL);

-- INSERT Statement 3
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'合同编码',6,N'合同类别名称',1,1,1,N'-',N'土地类',NULL),
	 (N'非合同编码',9,N'部门名称',1,1,0,N'-',N'工程部',NULL),
	 (N'付款申请编码',1,N'合同编码',1,0,0,N'-',N'SJHC.01-2010-8-0001',NULL),
	 (N'付款申请编码',2,N'项目代码',2,0,0,N'-',N'SJHC.01',NULL),
	 (N'付款申请编码',3,N'项目名称',1,0,0,N'-',N'四季花城一期',NULL),
	 (N'补充合同编码',1,N'主合同编码',1,1,1,N'-',N'SJHC.01-2010-8-0001',NULL),
	 (N'补充合同编码',4,N'项目代码',2,0,0,N'-',N'SJHC.01',NULL),
	 (N'补充合同编码',5,N'项目名称',1,0,0,N'-',N'四季花城一期',NULL),
	 (N'补充合同编码',6,N'公司代码',1,0,0,N'-',N'SZ',NULL),
	 (N'补充合同编码',7,N'公司名称',1,0,0,N'-',N'深圳公司',NULL);

-- INSERT Statement 4
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'补充合同编码',8,N'合同类别代码',1,0,0,N'-',N'001',NULL),
	 (N'补充合同编码',9,N'合同类别名称',1,0,0,N'-',N'土地类',NULL),
	 (N'补充合同编码',2,N'文本',1,1,1,N'-',N'补',NULL),
	 (N'补充合同编码',10,N'年份',4,0,0,N'-',N'2010',NULL),
	 (N'补充合同编码',11,N'月份',2,0,0,N'-',N'08',NULL),
	 (N'补充合同编码',12,N'流水号',4,0,0,N'-',N'0001',NULL),
	 (N'合同编码',5,N'文本',4,1,1,N'-',N'【万】字',NULL),
	 (N'合同编码',2,N'年份',4,1,1,N'-',N'2010',NULL),
	 (N'合同编码',13,N'月份',2,0,0,N'-',N'08',NULL),
	 (N'合同编码',14,N'流水号',4,0,0,N'-',N'0001',NULL);

-- INSERT Statement 5
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'非合同编码',13,N'合同类别代码',1,0,0,N'-',N'001',NULL),
	 (N'非合同编码',6,N'合同类别名称',1,1,1,N'-',N'土地类',NULL),
	 (N'非合同编码',8,N'文本',1,1,0,N'-',N'非',NULL),
	 (N'非合同编码',2,N'年份',4,1,1,N'-',N'2010',NULL),
	 (N'非合同编码',14,N'月份',2,1,0,N'-',N'08',NULL),
	 (N'非合同编码',15,N'流水号',4,0,0,N'-',N'0001',NULL),
	 (N'合同变更编码',6,N'合同类别代码',1,0,0,N'-',N'001',NULL),
	 (N'合同变更编码',7,N'合同类别名称',1,0,0,N'-',N'土地类',NULL),
	 (N'合同变更编码',8,N'变更类型代码',2,0,0,N'-',N'0.1',NULL),
	 (N'合同变更编码',9,N'变更类型名称',1,0,0,N'-',N'设计变更',NULL);

-- INSERT Statement 6
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'合同变更编码',10,N'文本',1,0,0,N'-',N'变',NULL),
	 (N'合同变更编码',11,N'变更年份',4,1,0,N'-',N'2010',NULL),
	 (N'合同变更编码',12,N'变更月份',2,1,0,N'-',N'08',NULL),
	 (N'合同变更编码',13,N'流水号',4,1,0,N'-',N'0001',NULL),
	 (N'付款申请编码',4,N'公司代码',1,1,1,N'-',N'SZ',NULL),
	 (N'付款申请编码',5,N'公司名称',1,0,0,N'-',N'深圳公司',NULL),
	 (N'付款申请编码',6,N'合同类别代码',1,0,0,N'-',N'001',NULL),
	 (N'付款申请编码',7,N'合同类别名称',1,0,0,N'-',N'土地类',NULL),
	 (N'付款申请编码',8,N'文本',1,0,0,N'-',N'付',NULL),
	 (N'付款申请编码',9,N'年份',4,1,0,N'-',N'2010',NULL);

-- INSERT Statement 7
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'付款申请编码',10,N'月份',2,1,0,N'-',N'08',NULL),
	 (N'付款申请编码',11,N'日',2,1,1,N'-',N'08',NULL),
	 (N'付款申请编码',12,N'流水号',4,1,0,N'-',N'0001',NULL),
	 (N'设计变更编码',1,N'公司代码',1,1,0,N'-',N'SZ',NULL),
	 (N'设计变更编码',2,N'公司名称',1,0,0,N'-',N'深圳公司',NULL),
	 (N'设计变更编码',3,N'项目代码',2,1,1,N'-',N'SJHC.01',NULL),
	 (N'设计变更编码',4,N'项目名称',1,0,0,N'-',N'四季花城一期',NULL),
	 (N'设计变更编码',5,N'部门代码',1,0,0,N'-',N'CB',NULL),
	 (N'设计变更编码',6,N'部门名称',1,0,0,N'-',N'成本部',NULL),
	 (N'设计变更编码',7,N'变更年份',4,1,0,N'-',N'2010',NULL);

-- INSERT Statement 8
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'设计变更编码',8,N'变更月份',2,1,0,N'-',N'08',NULL),
	 (N'设计变更编码',9,N'变更日期',1,1,0,N'-',N'08',NULL),
	 (N'设计变更编码',10,N'变更类型代码',2,0,0,N'-',N'0.1',NULL),
	 (N'设计变更编码',11,N'变更类型名称',1,0,0,N'-',N'设计变更',NULL),
	 (N'设计变更编码',12,N'文本',1,0,0,N'-',N'变',NULL),
	 (N'设计变更编码',13,N'流水号',4,1,0,N'-',N'0001',NULL),
	 (N'合同编码',3,N'区域编码',1,1,1,N'-',N'FT',N'单项目;多项目'),
	 (N'合同编码',7,N'审定编号',3,1,0,N'-',N'0001',N''),
	 (N'非合同编码',3,N'区域编码',1,1,1,N'-',N'FT',N'单项目;多项目'),
	 (N'非合同编码',7,N'审定编号',3,1,0,N'-',N'0001',N'');

-- INSERT Statement 9
INSERT INTO dotnet_erp352SP4.dbo.cb_CodeFormatTemplet (CodeType,Num,FieldNameChn,LevelLimit,IfIncluded,IfRestore,Separator,ExampleData,ProjTypes) VALUES
	 (N'非合同编码',5,N'文本2',1,1,1,N'-',N'跨期',N'多项目'),
	 (N'补充合同编码',3,N'审定编号',2,1,0,N'-',N'0001',N'');

