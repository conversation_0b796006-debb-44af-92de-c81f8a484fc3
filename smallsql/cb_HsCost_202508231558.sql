-- Extracted INSERT statements from: cb_HsCost_202508231558.sql
-- Extraction date: 2025-08-25 17:32:53
-- Total INSERT statements extracted: 100
-- Original file size: 9.6 MB
-- This file contains only the first 100 INSERT statements from the original

-- INSERT Statement 1
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'AE2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B02F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B12F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B22F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B32F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B42F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B52F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B62F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B72F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 2
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B82F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B92F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BA2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BB2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BC2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BD2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BE2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C02F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C12F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 3
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'C22F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C32F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C42F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C52F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C62F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C72F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C82F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C92F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CA2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CB2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 4
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'CC2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D02F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D12F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D22F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D32F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D42F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D52F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D62F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D92F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 5
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'DA2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DE2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E22F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E32F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E42F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E52F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E62F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E72F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E82F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 6
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'E92F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EA2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EB2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EC2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F02F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F12F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F22F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F32F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F42F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'A',N'A',N'项目成本',N'',1,0,N'按建筑面积',2,1,0,0.000000,N'系统生成数据',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 7
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'FD2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FE2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FF2F9BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'00309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'01309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'02309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'03309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'04309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'05309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'06309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 8
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'07309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'08309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'09309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0B309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0C309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'10309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 9
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'11309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'12309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'13309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'14309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'15309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'16309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'17309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'18309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'19309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 10
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'1B309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'20309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'21309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'22309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'23309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'24309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'25309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'28309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 11
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'29309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'31309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'32309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'33309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'34309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'35309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'36309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'37309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 12
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'38309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'39309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3B309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'40309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'41309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'42309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'43309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'01',N'A.01',N'开发成本',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 13
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'4C309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'50309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'51309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'52309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'53309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'54309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'55309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 14
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'56309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'57309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'58309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'59309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5B309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5C309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 15
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'60309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'61309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'62309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'63309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'64309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'65309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'66309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'67309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'68309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'69309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 16
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'6A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'70309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'71309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'72309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'73309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'74309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'77309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 17
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'78309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7C309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'80309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'81309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'82309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'83309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'84309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'85309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'86309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 18
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'87309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'88309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'89309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8A309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'90309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'91309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'92309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'02',N'A.02',N'期间费用',N'A',2,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 19
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'9B309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9C309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按指定比例',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9D309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9E309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9F309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A2309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A3309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A4309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 20
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'A5309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A6309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A7309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A8309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A9309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AA309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AB309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AD309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AE309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 21
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'AF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B2309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B3309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B4309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B5309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B6309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B7309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B8309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 22
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B9309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BD309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BE309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C2309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C3309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C6309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 23
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'C7309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CB309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D2309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D3309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D4309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D5309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 24
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'D6309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D7309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D8309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D9309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DD309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DE309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'01',N'A.01.01',N'土地费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 25
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'EA309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EB309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'ED309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EE309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F0309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F1309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F2309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F3309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 26
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'F4309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F5309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F6309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F7309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F8309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F9309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FA309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FB309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FC309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FD309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 27
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'FE309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FF309BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'00319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'01319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'02319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'03319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'04319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'05319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'06319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'07319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 28
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'08319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'10319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'11319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'12319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'15319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 29
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'16319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'20319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'21319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'22319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'23319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'24319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 30
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'25319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'26319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'27319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'28319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'30319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'02',N'A.01.02',N'前期工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 31
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'39319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'40319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'41319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'42319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 32
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'43319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'44319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'45319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'46319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'47319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'48319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'49319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 33
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'4D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'50319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'51319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'52319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'53319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'54319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'55319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'56319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 34
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'57319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'60319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'61319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'64319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 35
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'65319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'69319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'70319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'71319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'72319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'73319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 36
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'74319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'75319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'76319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'77319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'03',N'A.01.03',N'建安及装修工程费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 37
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'88319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'89319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'90319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'91319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 38
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'92319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'93319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'94319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'95319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'96319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'97319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'98319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'99319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9A319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9B319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 39
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'9C319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9D319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9E319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9F319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A0319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A1319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A2319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A3319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A4319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A5319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 40
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'A6319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AA319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AB319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AD319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AF319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B0319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B3319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 41
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B4319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B8319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BD319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BF319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C0319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C1319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C2319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 42
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'C3319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C4319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C5319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C6319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CA319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CB319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CD319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'04',N'A.01.04',N'社区管网费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 43
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'D7319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D8319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DA319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DB319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DD319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DF319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E0319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 44
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'E1319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E2319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E3319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E4319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E5319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E6319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E7319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E8319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EA319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 45
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'EB319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'ED319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EF319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F0319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F1319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F2319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F3319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F4319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 46
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'F5319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F8319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F9319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FA319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FB319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FC319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FD319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FE319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FF319BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'02329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 47
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'03329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'07329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'08329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'10329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'11329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 48
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'12329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'13329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'14329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'15329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'18329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'19329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'05',N'A.01.05',N'园林环境费用',N'A.01',3,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 49
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'26329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'27329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'28329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'29329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 50
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'30329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'31329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'32329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'33329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'34329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'35329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'36329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'37329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'38329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'39329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 51
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'3A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'40329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'41329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'42329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'43329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 52
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'44329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'47329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'48329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'49329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'51329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 53
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'52329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'56329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'57329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'60329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 54
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'61329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'62329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'63329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'64329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'67329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'68329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'69329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'06',N'A.01.06',N'配套设施费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 55
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'75329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'76329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'77329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'78329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'79329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 56
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'7F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'80329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'81329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'82329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'83329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'84329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'85329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'86329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'87329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'88329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 57
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'89329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8E329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8F329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'90329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'91329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'92329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 58
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'93329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'96329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'97329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'98329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'99329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9A329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9B329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9C329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9D329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A0329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 59
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'A1329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A5329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A6329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AC329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AD329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AE329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AF329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 60
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B0329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B1329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B2329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B3329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B6329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B7329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B8329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'07',N'A.01.07',N'开发间接费',N'A.01',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 61
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'C4329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C5329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C6329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C7329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C8329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CC329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CD329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 62
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'CE329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CF329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D0329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D1329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D2329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D3329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D4329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D5329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D6329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D7329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 63
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'D8329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DC329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DD329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DE329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DF329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E0329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E1329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 64
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'E2329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E5329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E6329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E7329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E8329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EC329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EF329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 65
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'F0329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F4329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F5329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F8329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F9329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FA329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FB329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FC329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FD329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'FE329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 66
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'FF329BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'00339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'01339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'02339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'05339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'06339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'07339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'08339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'09339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'01',N'A.02.01',N'营销费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 67
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'13339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'14339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'15339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'16339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'17339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'18339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'19339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 68
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'1D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'20339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'21339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'22339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'23339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'24339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'25339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'26339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 69
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'27339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'28339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'29339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'30339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 70
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'31339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'34339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'35339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'36339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'37339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'38339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'39339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 71
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'3F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'43339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'44339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'47339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'48339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'49339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 72
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'4E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'4F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'50339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'51339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'54339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'55339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'56339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'57339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'58339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'59339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'02',N'A.02.02',N'管理费用',N'A.02',3,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 73
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'62339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'63339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,N'按指定比例',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'64339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'65339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'66339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'67339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'68339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'69339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 74
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'6C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'70339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'71339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'72339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'73339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'74339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'75339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 75
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'76339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'77339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'78339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'79339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 76
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'80339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'83339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'84339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'85339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'86339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'87339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'88339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'89339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 77
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'8E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'92339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'93339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'96339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'97339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'98339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'99339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9A339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9B339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9C339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 78
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'9D339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9E339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9F339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A0339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A3339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A4339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A8339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'01',N'A.01.01.01',N'政府地价及相关费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 79
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B1339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B2339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,N'按指定比例',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B3339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B4339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B8339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B9339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BA339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 80
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'BB339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BC339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BD339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BE339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BF339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C0339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C1339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C2339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C3339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C4339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 81
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'C5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C8339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C9339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CA339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CB339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CC339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CD339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'CE339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 82
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'CF339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D2339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D3339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D4339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D8339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'D9339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'DC339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 83
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'DD339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E1339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E2339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E8339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'E9339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EA339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EB339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 84
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'EC339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'ED339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EE339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'EF339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F2339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F3339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F4339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F5339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F6339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'F7339BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'02',N'A.01.01.02',N'拆迁补偿费',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 85
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'00349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'01349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,N'按指定比例',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'02349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'03349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'04349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'05349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'06349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'07349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'08349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'09349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 86
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'0A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0E349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'0F349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'10349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'11349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'12349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'13349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 87
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'14349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'15349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'16349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'17349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'18349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'19349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'1D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 88
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'1E349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'21349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'22349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'23349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'24349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'25349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'26349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'27349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'28349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'2B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 89
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'2C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'30349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'31349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'34349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'35349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'36349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'37349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'38349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'39349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 90
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'3B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'3E349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'41349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'42349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'43349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'44349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'45349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'46349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'03',N'A.01.01.03',N'其他土地费用',N'A.01.01',4,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 91
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'4F349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'50349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'51349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'52349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'53349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'54349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'55349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'56349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'57349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'58349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 92
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'59349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5E349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'5F349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'60349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'61349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'62349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 93
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'63349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'64349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'65349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'66349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'67349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'68349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'69349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'6C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 94
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'6D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'70349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'71349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'72349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'73349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'74349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'75349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'76349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'77349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 95
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'7B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018.XSDLDJ',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'7F349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'80349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.05.05',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'83349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'84349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.JTZB.zb',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'85349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'86349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.02',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'87349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTHF.QTHF-2-2',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'88349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'89349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.QTYLCYY.一期',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 96
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'8A349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8B349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.002',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8C349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.RuiAn',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'8D349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.RuiAn.ZHIFANG',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'90349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'91349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.01',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'92349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.02',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'93349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.03',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'94349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.04',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'95349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.XSCYY.05',N'01',N'A.01.02.01',N'勘察、设计费',N'A.01.02',4,0,N'按建筑面积',0,1,0,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 97
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'9E349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'9F349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—A',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,N'按建筑面积',NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A0349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—B',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A1349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—C',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A2349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.003.QTC—D',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A3349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A4349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL1',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A5349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL2',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A6349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL3',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A7349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.004.QT—CYL4',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 98
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'A8349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'A9349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.005.QTHF',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AA349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AB349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.007.QTHS',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AC349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AD349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.008.QTHXD',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AE349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'AF349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.009.QTHXD2',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B0349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B1349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.010.QTJT',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 99
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'B2349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B3349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—A',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B4349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—B',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B5349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.011.QTXZ—C',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B6349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B7349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D1',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B8349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZD12',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'B9349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—D2',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BA349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E1',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BB349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.QTXZ—E2',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

-- INSERT Statement 100
INSERT INTO dotnet_erp352SP4.dbo.cb_HsCost (HsCostGUID,BUGUID,ProjectCode,CostShortCode,CostCode,CostShortName,ParentCode,CostLevel,IsEndCost,FtMode,IsJianAn,IsForecast,IsEndForecast,ForecastCost,Remarks,JsCost,CSFtMode,HsFtMode,CSFtRate,HsFtRate,GetMode,CsjcState,CsjcCode,CsjcName,XsUnit,GclUnit) VALUES
	 (N'BC349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.012.xz21',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'BF349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C0349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.014.SHGC—2',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C1349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C2349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.HYC—BEI',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C3349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N1DK',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C4349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.015.N2DK',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C5349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C6349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.016.HHJY',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL),
	 (N'C9349BCB-DF3A-EC11-80E0-005056A9F45C',N'132D3F3F-3C16-4BC7-A4DB-EE53CAF6A3D9',N'qddq.018',N'01',N'A.01.02.01.01',N'勘察丈量测绘费用',N'A.01.02.01',5,1,N'按建筑面积',0,1,1,0.000000,N'',0.0000,NULL,NULL,0.0000,0.0000,NULL,0,NULL,NULL,NULL,NULL);

