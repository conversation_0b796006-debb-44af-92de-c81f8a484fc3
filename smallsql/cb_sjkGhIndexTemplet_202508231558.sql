-- Extracted INSERT statements from: cb_sjkGhIndexTemplet_202508231558.sql
-- Extraction date: 2025-08-25 17:32:50
-- Total INSERT statements extracted: 7
-- Original file size: 0.0 MB
-- This file contains only the first 100 INSERT statements from the original

-- INSERT Statement 1
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'产品指标',1,N'cp',N'cp',N'',N'',N'',N'',N'',1,N'',N''),
	 (N'建设用地面积',2,N'001',N'cp.001',N'cp',N'产品指标-建设用地面积',N'cb_sjkProductIndex',N'BuildTerraArea',N'产品',0,N'平方米',N''),
	 (N'套内面积',2,N'002',N'cp.002',N'cp',N'产品指标-套内面积',N'cb_sjkProductIndex',N'InnerArea',N'产品',0,N'平方米',N''),
	 (N'占地面积',2,N'003',N'cp.003',N'cp',N'产品指标-占地面积',N'cb_sjkProductIndex',N'OccupyArea',N'产品',0,N'平方米',N''),
	 (N'建筑面积',2,N'004',N'cp.004',N'cp',N'产品指标-建筑面积',N'cb_sjkProductIndex',N'BuildArea',N'产品',0,N'平方米',N''),
	 (N'地上建筑面积',2,N'005',N'cp.005',N'cp',N'产品指标-地上建筑面积',N'cb_sjkProductIndex',N'UpperBuildArea',N'产品',0,N'平方米',N''),
	 (N'地下建筑面积',2,N'006',N'cp.006',N'cp',N'产品指标-地下建筑面积',N'cb_sjkProductIndex',N'UnderBuildArea',N'产品',0,N'平方米',N''),
	 (N'可售面积',2,N'007',N'cp.007',N'cp',N'产品指标-可售面积',N'cb_sjkProductIndex',N'SaleArea',N'产品',0,N'平方米',N''),
	 (N'地上可售面积',2,N'008',N'cp.008',N'cp',N'产品指标-地上可售面积',N'cb_sjkProductIndex',N'UpperSaleArea',N'产品',0,N'平方米',N''),
	 (N'地下可售面积',2,N'009',N'cp.009',N'cp',N'产品指标-地下可售面积',N'cb_sjkProductIndex',N'UnderSaleArea',N'产品',0,N'平方米',N'');

-- INSERT Statement 2
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'可租面积',2,N'010',N'cp.010',N'cp',N'产品指标-可租面积',N'cb_sjkProductIndex',N'LendArea',N'产品',0,N'平方米',N''),
	 (N'地上可租面积',2,N'011',N'cp.011',N'cp',N'产品指标-地上可租面积',N'cb_sjkProductIndex',N'UpperLendArea',N'产品',0,N'平方米',N''),
	 (N'地下可租面积',2,N'012',N'cp.012',N'cp',N'产品指标-地下可租面积',N'cb_sjkProductIndex',N'UnderLendArea',N'产品',0,N'平方米',N''),
	 (N'赠送面积',2,N'013',N'cp.013',N'cp',N'产品指标-赠送面积',N'cb_sjkProductIndex',N'LargessArea',N'产品',0,N'平方米',N''),
	 (N'实际建设面积',2,N'014',N'cp.014',N'cp',N'产品指标-实际建设面积',N'cb_sjkProductIndex',N'FactBuildArea',N'产品',0,N'平方米',N''),
	 (N'栋数',2,N'015',N'cp.015',N'cp',N'产品指标-栋数',N'cb_sjkProductIndex',N'BuildNum',N'产品',0,N'栋',N''),
	 (N'层数',2,N'016',N'cp.016',N'cp',N'产品指标-栋数',N'cb_sjkProductIndex',N'FloorNum',N'产品',0,N'层',N''),
	 (N'单元数',2,N'017',N'cp.017',N'cp',N'产品指标-单元数',N'cb_sjkProductIndex',N'CellNum',N'产品',0,N'个',N''),
	 (N'户数',2,N'018',N'cp.018',N'cp',N'产品指标-户数',N'cb_sjkProductIndex',N'UserNum',N'产品',0,N'户',N''),
	 (N'项目指标',1,N'xm',N'xm',N'',N'',N'',N'',N'',1,N'',N'');

-- INSERT Statement 3
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'总用地面积',2,N'001',N'xm.001',N'xm',N'项目指标-总用地面积',N'cb_sjkProjectIndex',N'AllTerraArea',N'项目',0,N'平方米',N''),
	 (N'建设用地面积',2,N'002',N'xm.002',N'xm',N'项目指标-建设用地面积',N'cb_sjkProjectIndex',N'BuildTerraArea',N'项目',0,N'平方米',N''),
	 (N'套内面积',2,N'003',N'xm.003',N'xm',N'项目指标-套内面积',N'cb_sjkProjectIndex',N'InnerArea',N'项目',0,N'平方米',N''),
	 (N'占地面积',2,N'004',N'xm.004',N'xm',N'项目指标-占地面积',N'cb_sjkProjectIndex',N'OccupyArea',N'项目',0,N'平方米',N''),
	 (N'道路面积',2,N'005',N'xm.005',N'xm',N'项目指标-道路面积',N'cb_sjkProjectIndex',N'RoadArea',N'项目',0,N'平方米',N''),
	 (N'景观面积',2,N'006',N'xm.006',N'xm',N'项目指标-景观面积',N'cb_sjkProjectIndex',N'SightArea',N'项目',0,N'平方米',N''),
	 (N'容积率',2,N'007',N'xm.007',N'xm',N'项目指标-容积率',N'cb_sjkProjectIndex',N'CubageRate',N'项目',0,N'',N''),
	 (N'建筑密度',2,N'008',N'xm.008',N'xm',N'项目指标-建筑密度',N'cb_sjkProjectIndex',N'BuildDensity',N'项目',0,N'',N''),
	 (N'异地绿化面积',2,N'009',N'xm.009',N'xm',N'项目指标-异地绿化面积',N'cb_sjkProjectIndex',N'YdlhArea',N'项目',0,N'平方米',N''),
	 (N'建筑面积',2,N'010',N'xm.010',N'xm',N'项目指标-建筑面积',N'cb_sjkProjectIndex',N'BuildArea',N'项目',0,N'平方米',N'');

-- INSERT Statement 4
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'地上建筑面积',2,N'011',N'xm.011',N'xm',N'项目指标-地上建筑面积',N'cb_sjkProjectIndex',N'UpperBuildArea',N'项目',0,N'平方米',N''),
	 (N'地下建筑面积',2,N'012',N'xm.012',N'xm',N'项目指标-地下建筑面积',N'cb_sjkProjectIndex',N'UnderBuildArea',N'项目',0,N'平方米',N''),
	 (N'可售面积',2,N'013',N'xm.013',N'xm',N'项目指标-可售面积',N'cb_sjkProjectIndex',N'SaleArea',N'项目',0,N'平方米',N''),
	 (N'地上可售面积',2,N'014',N'xm.014',N'xm',N'项目指标-地上可售面积',N'cb_sjkProjectIndex',N'UpperSaleArea',N'项目',0,N'平方米',N''),
	 (N'地下可售面积',2,N'015',N'xm.015',N'xm',N'项目指标-地下可售面积',N'cb_sjkProjectIndex',N'UnderSaleArea',N'项目',0,N'平方米',N''),
	 (N'可租面积',2,N'016',N'xm.016',N'xm',N'项目指标-可租面积',N'cb_sjkProjectIndex',N'LendArea',N'项目',0,N'平方米',N''),
	 (N'地上可租面积',2,N'017',N'xm.017',N'xm',N'项目指标-可租面积',N'cb_sjkProjectIndex',N'UpperLendArea',N'项目',0,N'平方米',N''),
	 (N'地下可租面积',2,N'018',N'xm.018',N'xm',N'项目指标-地下可租面积',N'cb_sjkProjectIndex',N'UnderLendArea',N'项目',0,N'平方米',N''),
	 (N'赠送面积',2,N'019',N'xm.019',N'xm',N'项目指标-赠送面积',N'cb_sjkProjectIndex',N'LargessArea',N'项目',0,N'平方米',N''),
	 (N'实际建设面积',2,N'020',N'xm.020',N'xm',N'项目指标-实际建设面积',N'cb_sjkProjectIndex',N'FactBuildArea',N'项目',0,N'平方米',N'');

-- INSERT Statement 5
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'实际容积率',2,N'021',N'xm.021',N'xm',N'项目指标-实际容积率',N'cb_sjkProjectIndex',N'FactCubageRate',N'项目',0,N'',N''),
	 (N'车位数',2,N'022',N'xm.022',N'xm',N'项目指标-车位数',N'cb_sjkProjectIndex',N'CwNum',N'项目',0,N'个',N''),
	 (N'地上车位数',2,N'023',N'xm.023',N'xm',N'项目指标-地上车位数',N'cb_sjkProjectIndex',N'UpperCwNum',N'项目',0,N'个',N''),
	 (N'地下车位数',2,N'024',N'xm.024',N'xm',N'项目指标-地下车位数',N'cb_sjkProjectIndex',N'UnderCwNum',N'项目',0,N'个',N''),
	 (N'周界长度',2,N'025',N'xm.025',N'xm',N'项目指标-周界长度',N'cb_sjkProjectIndex',N'ZjLength',N'项目',0,N'米',N''),
	 (N'大门个数',2,N'026',N'xm.026',N'xm',N'项目指标-大门个数',N'cb_sjkProjectIndex',N'GateNum',N'项目',0,N'个',N''),
	 (N'软景面积',2,N'027',N'xm.027',N'xm',N'项目指标-软景面积',N'cb_sjkProjectIndex',N'RjArea',N'项目',0,N'平方米',N''),
	 (N'软景面积所占比例',2,N'028',N'xm.028',N'xm',N'项目指标-软景面积所占比例',N'cb_sjkProjectIndex',N'RjAreaRate',N'项目',0,N'',N''),
	 (N'硬景面积',2,N'029',N'xm.029',N'xm',N'项目指标-硬景面积',N'cb_sjkProjectIndex',N'YjArea',N'项目',0,N'平方米',N''),
	 (N'硬景面积所占比例',2,N'030',N'xm.030',N'xm',N'项目指标-硬景面积所占比例',N'cb_sjkProjectIndex',N'YjAreaRate',N'项目',0,N'',N'');

-- INSERT Statement 6
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'水景面积',2,N'031',N'xm.031',N'xm',N'项目指标-水景面积',N'cb_sjkProjectIndex',N'SjArea',N'项目',0,N'平方米',N''),
	 (N'水景面积所占比例',2,N'032',N'xm.032',N'xm',N'项目指标-水景面积所占比例',N'cb_sjkProjectIndex',N'SjAreaRate',N'项目',0,N'',N''),
	 (N'区外水接入长度',2,N'033',N'xm.033',N'xm',N'项目指标-区外水接入长度',N'cb_sjkProjectIndex',N'QwsLength',N'项目',0,N'米',N''),
	 (N'区外电接入长度',2,N'034',N'xm.034',N'xm',N'项目指标-区外电接入长度',N'cb_sjkProjectIndex',N'QwdLength',N'项目',0,N'米',N''),
	 (N'区外气接入长度',2,N'035',N'xm.035',N'xm',N'项目指标-区外气接入长度',N'cb_sjkProjectIndex',N'QwqLength',N'项目',0,N'米',N''),
	 (N'区外热力接入长度',2,N'036',N'xm.036',N'xm',N'项目指标-区外气接入长度',N'cb_sjkProjectIndex',N'QwrlLength',N'项目',0,N'米',N''),
	 (N'开闭所容量',2,N'037',N'xm.037',N'xm',N'项目指标-开闭所容量',N'cb_sjkProjectIndex',N'Kbsrj',N'项目',0,N'KW',N''),
	 (N'配电房容量',2,N'038',N'xm.038',N'xm',N'项目指标-配电房容量',N'cb_sjkProjectIndex',N'Pdfrj',N'项目',0,N'KW',N''),
	 (N'配电房个数',2,N'039',N'xm.039',N'xm',N'项目指标-配电房个数',N'cb_sjkProjectIndex',N'PdfNum',N'项目',0,N'个',N''),
	 (N'发电机功率',2,N'040',N'xm.040',N'xm',N'项目指标-发电机功率',N'cb_sjkProjectIndex',N'Fdjgl',N'项目',0,N'KW',N'');

-- INSERT Statement 7
INSERT INTO dotnet_erp352SP4.dbo.cb_sjkGhIndexTemplet (IndexName,IndexLevel,IndexShortCode,IndexCode,ParentCode,GetValue,TableName,ColumnName,IndexType,IsXt,Unit,Remarks) VALUES
	 (N'挖方工程量',2,N'041',N'xm.041',N'xm',N'项目指标-挖方工程量',N'cb_sjkProjectIndex',N'WfQty',N'项目',0,N'立方米',N''),
	 (N'填方工程量',2,N'042',N'xm.042',N'xm',N'项目指标-填方工程量',N'cb_sjkProjectIndex',N'TfQty',N'项目',0,N'立方米',N''),
	 (N'外运工程量',2,N'043',N'xm.043',N'xm',N'项目指标-外运工程量',N'cb_sjkProjectIndex',N'WyQty',N'项目',0,N'立方米',N'');

