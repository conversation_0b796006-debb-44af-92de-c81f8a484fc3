# SQL批量执行工具

一个功能强大的SQL批量执行工具，支持批量执行指定文件夹下的SQL文件，具有配置文件管理、日志记录、表数据清理等功能。

## 功能特性

- ✅ 批量执行SQL文件
- ✅ 支持配置文件管理
- ✅ 详细的日志记录
- ✅ 自动表数据清理
- ✅ **数据行数限制**：支持限制每张表只导入前N条数据
- ✅ 文件大小限制
- ✅ 递归文件夹搜索
- ✅ 错误处理和恢复
- ✅ 执行统计报告

## 安装依赖

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 安装ODBC驱动（macOS）
```bash
# 添加Microsoft仓库
brew tap microsoft/mssql-release

# 安装ODBC Driver 17
brew install microsoft/mssql-release/msodbcsql17

# 安装unixODBC（如果未安装）
brew install unixodbc
```

### 3. 验证安装
```bash
# 检查已安装的ODBC驱动
odbcinst -q -d

# 测试数据库连接
python test_odbc.py
```

## 配置文件

编辑 `config.json` 文件，配置数据库连接和执行参数：

```json
{
    "database": {
        "server": "your_server",
        "database": "your_database",
        "username": "your_username",
        "password": "your_password",
        "driver": "ODBC Driver 17 for SQL Server",
        "timeout": 30,
        "autocommit": true
    },
    "execution": {
        "max_file_size_mb": 500,
        "clear_tables_before_execution": true,
        "skip_empty_files": true,
        "default_sql_folder": "/path/to/your/sql/files",
        "recursive_search": true,
        "limit_rows_per_table": 100,
        "enable_row_limit": true
    },
    "logging": {
        "level": "INFO",
        "log_file": "sql_execution.log",
        "console_output": true
    }
}
```

## 使用方法

### 方法1：使用启动脚本（推荐）
```bash
# 使用默认路径（配置文件中的default_sql_folder）
./run_sql_executor.sh

# 指定SQL文件夹路径
./run_sql_executor.sh /path/to/sql/files

# 启用详细输出
./run_sql_executor.sh /path/to/sql/files --verbose

# 限制每张表只导入前50条数据
./run_sql_executor.sh /path/to/sql/files --limit-rows 50

# 禁用行数限制，导入全部数据
./run_sql_executor.sh /path/to/sql/files --no-limit
```

### 方法2：直接运行Python脚本
```bash
# 设置环境变量
export ODBCSYSINI=/opt/homebrew/etc

# 运行脚本
python new_sql_batch_executor.py /path/to/sql/files --verbose
```

### 命令行参数

- `folder`：SQL文件夹路径（可选，未指定时使用配置文件中的默认路径）
- `--verbose`：启用详细输出模式
- `--help`：显示帮助信息
- `--limit-rows`：每张表限制导入的行数，覆盖配置文件设置
- `--no-limit`：禁用行数限制功能

## 文件结构

```
.
├── new_sql_batch_executor.py    # 主执行脚本
├── config.json                  # 配置文件
├── requirements.txt             # Python依赖
├── run_sql_executor.sh          # 启动脚本
├── test_odbc.py                 # ODBC连接测试脚本
├── README.md                    # 使用说明
└── sql_execution.log            # 执行日志（自动生成）
```

## 日志文件

执行日志会保存在 `sql_execution.log` 文件中，包含：
- 执行开始和结束时间
- 数据库连接状态
- 文件处理详情
- 错误信息和堆栈跟踪
- 执行统计信息

## 故障排除

### 1. ODBC驱动问题
如果遇到 "Can't open lib" 错误：
```bash
# 检查驱动是否正确安装
odbcinst -q -d

# 重新安装驱动
brew reinstall msodbcsql17

# 设置环境变量
export ODBCSYSINI=/opt/homebrew/etc
```

### 2. 数据库连接问题
- 检查服务器地址和端口
- 验证用户名和密码
- 确认数据库名称正确
- 检查网络连接

### 3. SQL文件编码问题
- 确保SQL文件使用UTF-8编码
- 检查文件是否包含BOM标记

## 数据行数限制功能

### 功能说明

数据行数限制功能可以帮助您在测试环境中快速导入数据样本，避免因数据量过大导致的长时间等待。

### 工作原理

- 自动检测INSERT语句中的SELECT子句
- 在SELECT后添加`TOP N`限制（N为配置的行数）
- 只对INSERT...SELECT语句生效
- 如果SQL中已有TOP限制，则不会重复添加

### 配置方式

1. **配置文件方式**：在`config.json`中设置
   ```json
   "enable_row_limit": true,
   "limit_rows_per_table": 100
   ```

2. **命令行方式**：使用参数覆盖配置
   ```bash
   --limit-rows 50    # 限制为50行
   --no-limit         # 禁用限制
   ```

## 注意事项

1. **数据安全**：脚本会在执行SQL前清空表数据（如果启用），请谨慎使用
2. **文件大小**：超过配置限制的大文件会被跳过
3. **事务处理**：建议启用自动提交模式
4. **权限要求**：确保数据库用户有足够的权限执行SQL操作
5. **行数限制功能**：只对INSERT...SELECT语句有效

## 许可证

MIT License