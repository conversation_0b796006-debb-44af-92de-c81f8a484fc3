{"database": {"server": "************", "database": "dotnet_erp352SP4", "username": "Hz", "password": "Aa123123", "driver": "ODBC Driver 17 for SQL Server", "timeout": 30, "autocommit": true}, "execution": {"max_file_size_mb": 150000, "clear_tables_before_execution": true, "skip_empty_files": true, "default_sql_folder": "/Users/<USER>/Documents/五、青特项目/成本数据库/cb_data_clean_end", "recursive_search": true, "limit_rows_per_table": 100, "enable_row_limit": true}, "logging": {"level": "INFO", "log_file": "sql_execution.log", "console_output": true}, "_comments": {"database": {"server": "SQL Server服务器地址，如：localhost 或 *************", "database": "目标数据库名称", "username": "数据库用户名", "password": "数据库密码", "driver": "ODBC驱动程序名称，常见的有：ODBC Driver 18 for SQL Server, ODBC Driver 17 for SQL Server", "timeout": "连接超时时间（秒）", "autocommit": "是否自动提交事务，建议设为true"}, "execution": {"max_file_size_mb": "最大文件大小限制(MB)，超过此大小的文件将被跳过", "clear_tables_before_execution": "执行SQL前是否先清空表数据", "skip_empty_files": "是否跳过空文件", "default_sql_folder": "默认SQL文件夹路径，当命令行未指定时使用", "recursive_search": "是否递归搜索子文件夹中的SQL文件", "limit_rows_per_table": "每张表限制导入的数据行数", "enable_row_limit": "是否启用行数限制功能"}, "logging": {"level": "日志级别: DEBUG, INFO, WARNING, ERROR", "log_file": "日志文件名", "console_output": "是否在控制台输出日志"}}}