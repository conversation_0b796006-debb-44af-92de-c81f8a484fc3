# SQL批量执行工具 (SQL Batch Executor)

## 概述

这是一个功能强大的SQL批量执行工具，专门用于批量执行指定文件夹下的SQL文件。该工具支持配置文件管理、详细日志记录、表数据清理以及行数限制等高级功能。

## 主要功能

### 核心功能
- **批量执行SQL文件**: 自动扫描指定文件夹下的所有.sql文件并按顺序执行
- **数据库连接管理**: 支持SQL Server数据库连接，使用ODBC驱动
- **配置文件支持**: 通过JSON配置文件管理数据库连接和执行参数
- **日志记录**: 详细的执行日志，支持文件和控制台输出
- **执行统计**: 提供详细的执行摘要和统计信息

### 高级功能
- **表数据清理**: 执行前可自动清空目标表数据
- **文件大小限制**: 跳过超过指定大小的SQL文件
- **空文件跳过**: 自动跳过空的SQL文件
- **行数限制**: 支持限制每张表导入的数据行数
- **SQL语句分割**: 智能识别GO和分号分隔的SQL语句
- **表名提取**: 自动从SQL内容中提取表名用于数据清理

## 安装要求

### Python依赖
```bash
pip install pyodbc
```

### 系统要求
- Python 3.6+
- SQL Server ODBC驱动程序 (ODBC Driver 17 for SQL Server)

## 配置文件

工具使用`config.json`配置文件，首次运行时会自动创建默认配置：

```json
{
    "database": {
        "server": "localhost",
        "database": "test_db",
        "username": "sa",
        "password": "password",
        "driver": "ODBC Driver 17 for SQL Server"
    },
    "execution": {
        "max_file_size_mb": 100,
        "clear_tables_before_execution": true,
        "skip_empty_files": true,
        "default_sql_folder": "",
        "enable_row_limit": false,
        "limit_rows_per_table": 100
    },
    "logging": {
        "level": "INFO",
        "log_file": "sql_execution.log",
        "console_output": true
    }
}
```

### 配置说明

#### 数据库配置 (database)
- `server`: 数据库服务器地址
- `database`: 目标数据库名称
- `username`: 数据库用户名
- `password`: 数据库密码
- `driver`: ODBC驱动程序名称

#### 执行配置 (execution)
- `max_file_size_mb`: 最大文件大小限制(MB)
- `clear_tables_before_execution`: 是否在执行前清空表数据
- `skip_empty_files`: 是否跳过空文件
- `default_sql_folder`: 默认SQL文件夹路径
- `enable_row_limit`: 是否启用行数限制功能
- `limit_rows_per_table`: 每张表限制导入的行数

#### 日志配置 (logging)
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `log_file`: 日志文件路径
- `console_output`: 是否输出到控制台

## 使用方法

### 基本用法

```bash
# 使用默认配置执行指定文件夹
python new_sql_batch_executor.py /path/to/sql/files

# 使用自定义配置文件
python new_sql_batch_executor.py /path/to/sql/files -c custom_config.json

# 使用配置文件中的默认路径
python new_sql_batch_executor.py
```

### 命令行参数

- `folder`: SQL文件夹路径（可选，未指定时使用配置文件中的默认路径）
- `-c, --config`: 配置文件路径（默认: config.json）
- `-v, --verbose`: 启用详细输出（DEBUG级别日志）
- `--limit-rows N`: 限制每张表导入N行数据
- `--no-limit`: 禁用行数限制功能

### 使用示例

```bash
# 基本执行
python new_sql_batch_executor.py ./sql_files

# 详细输出模式
python new_sql_batch_executor.py ./sql_files -v

# 限制每张表只导入1000行数据
python new_sql_batch_executor.py ./sql_files --limit-rows 1000

# 禁用行数限制
python new_sql_batch_executor.py ./sql_files --no-limit

# 使用自定义配置
python new_sql_batch_executor.py ./sql_files -c production_config.json
```

## 功能特性详解

### 智能表名提取
工具能够自动从SQL内容中提取表名，支持：
- `INSERT INTO` 语句
- `CREATE TABLE` 语句
- 带schema前缀的表名处理

### 行数限制功能
当启用行数限制时：
- 只对INSERT语句生效
- 每个文件独立计数
- 达到限制后停止处理当前文件的后续INSERT语句
- 其他类型的SQL语句不受影响

### 文件处理策略
- 按文件名排序执行
- 支持GO和分号分隔的SQL语句
- 自动跳过注释和空行
- 详细的错误处理和日志记录

## 执行结果

执行完成后会显示详细的统计摘要：

```
============================================================
执行摘要
============================================================
总文件数: 10
处理文件数: 8
成功文件数: 7
失败文件数: 1
跳过文件数: 2
清理表数: 5
成功率: 87.5%
执行时间: 45.32秒
日志文件: sql_execution.log
============================================================
```

## 日志文件

所有执行过程都会记录到日志文件中，包括：
- 数据库连接状态
- 文件扫描结果
- SQL执行详情
- 错误信息和警告
- 执行统计

## 错误处理

工具提供完善的错误处理机制：
- 数据库连接失败处理
- SQL语句执行错误捕获
- 文件读取异常处理
- 配置文件错误处理
- 用户中断处理

## 退出码

- `0`: 执行成功
- `1`: 执行失败或发生错误
- `130`: 用户中断执行

## 注意事项

1. **数据安全**: 启用表数据清理功能时，会删除目标表的所有数据，请谨慎使用
2. **文件顺序**: SQL文件按文件名排序执行，建议使用数字前缀控制执行顺序
3. **事务处理**: 每个SQL语句独立提交，失败时不会回滚已执行的语句
4. **权限要求**: 确保数据库用户具有足够的权限执行SQL操作
5. **驱动程序**: 确保系统已安装正确版本的SQL Server ODBC驱动程序

## 故障排除

### 常见问题

1. **连接失败**: 检查数据库服务器地址、端口、用户名和密码
2. **驱动程序错误**: 确认已安装ODBC Driver 17 for SQL Server
3. **权限不足**: 确保数据库用户具有相应的操作权限
4. **文件编码**: 确保SQL文件使用UTF-8编码

### 调试建议

- 使用`-v`参数启用详细输出
- 检查日志文件获取详细错误信息
- 验证配置文件格式和内容
- 测试数据库连接是否正常

## 版本信息

- 版本: 1.0
- 作者: [作者信息]
- 最后更新: 2024年
- Python版本要求: 3.6+