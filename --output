USE [dotnet_erp352SP4]
GO

-- TABLE: cb_AddDevelopCost
CREATE TABLE [dbo].[cb_AddDevelopCost](
	[BuLuFTCostGUID] [uniqueidentifier] NOT NULL,
	[ContractCode] [varchar](200) NULL,
	[ContractName] [varchar](80) NULL,
	[HtTypeCode] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[PayDate] [smalldatetime] NULL,
	[PayAmount] [money] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[JbDeptName] [varchar](50) NULL,
	[UseCostInfo] [nvarchar](1000) NULL,
	[JfProviderName] [varchar](100) NULL,
	[InputTaxAmount] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
 CONSTRAINT [PK_CB_ADDDEVELOPCOST] PRIMARY KEY CLUSTERED 
(
	[BuLuFTCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Adjust
CREATE TABLE [dbo].[cb_Adjust](
	[AdjustGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[AdjustType] [varchar](20) NULL,
	[AdjustDate] [datetime] NULL,
	[Adjuster] [varchar](20) NULL,
	[ApproveState] [varchar](20) NULL,
	[Remarks] [text] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[InureDate] [datetime] NULL,
	[TargetCost] [money] NULL,
	[CsfaGUID] [uniqueidentifier] NULL,
	[IsSyncCost] [int] NULL,
	[SyncUserGUID] [uniqueidentifier] NULL,
	[SyncDate] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_AdjustCBProfitRate
CREATE TABLE [dbo].[cb_AdjustCBProfitRate](
	[CBProfitRateGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[PlanSaleAmount] [money] NULL,
	[TargetCost] [money] NULL,
	[PubBuildFtCost] [money] NULL,
	[SaleTaxExtra] [money] NULL,
	[SaleTaxExtraRate] [money] NULL,
	[LandIncrementTax] [money] NULL,
	[BusinessTax] [money] NULL,
	[BusinessTaxRate] [money] NULL,
	[PlanProfit] [money] NULL,
	[PlanProfitRate] [money] NULL,
	[AdjustGUID] [uniqueidentifier] NULL,
	[IsAfterAdjust] [bit] NULL,
 CONSTRAINT [PK_CB_ADJUSTCBPROFITRATE] PRIMARY KEY CLUSTERED 
(
	[CBProfitRateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_AdjustDtl
CREATE TABLE [dbo].[cb_AdjustDtl](
	[AdjustDtlGUID] [uniqueidentifier] NULL,
	[AdjustGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[IsSwitchTo] [varchar](50) NULL,
	[AdjustAmount] [money] NULL,
	[Remarks] [text] NULL,
	[PreAdjustLayoutSpare] [money] NULL,
	[ExcludingTaxAdjustAmount] [money] NOT NULL,
	[InputTaxAmount] [money] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_AdjustHistory
CREATE TABLE [dbo].[cb_AdjustHistory](
	[AdjustHistoryGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[AdjustName] [varchar](200) NULL,
	[AdjustGUID] [uniqueidentifier] NULL,
	[ApproveDate] [datetime] NULL,
	[Adjuster] [varchar](20) NULL,
 CONSTRAINT [PK_CB_ADJUSTHISTORY] PRIMARY KEY CLUSTERED 
(
	[AdjustHistoryGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_AdjustHistoryDtl
CREATE TABLE [dbo].[cb_AdjustHistoryDtl](
	[AdjustHistoryDtlGUID] [uniqueidentifier] NOT NULL,
	[AdjustHistoryGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjCode] [varchar](100) NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostName] [varchar](40) NULL,
	[CostCode] [varchar](100) NULL,
	[IfEndCost] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
	[TargetCost] [money] NULL,
	[AdjustCost] [money] NULL,
	[ExcludingTaxTargetCost] [money] NOT NULL,
	[ExcludingTaxAdjustCost] [money] NOT NULL,
 CONSTRAINT [PK_CB_ADJUSTHISTORYDTL] PRIMARY KEY CLUSTERED 
(
	[AdjustHistoryDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_AlterOrderCode
CREATE TABLE [dbo].[cb_AlterOrderCode](
	[AlterOrderCodeGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[MaxNum] [int] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_AutoFtLog
CREATE TABLE [dbo].[cb_AutoFtLog](
	[AutoFtLogGUID] [uniqueidentifier] NOT NULL,
	[BatchGuid] [uniqueidentifier] NULL,
	[ProjGuid] [uniqueidentifier] NULL,
	[ProjNane] [varchar](500) NULL,
	[BeginTime] [datetime] NULL,
	[EndTime] [datetime] NULL,
	[BillCount] [int] NULL,
	[ProcessIndex] [varchar](50) NULL,
	[IsSucess] [int] NULL,
	[FailReason] [varchar](500) NULL,
 CONSTRAINT [PK_cb_AutoFtLog] PRIMARY KEY CLUSTERED 
(
	[AutoFtLogGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BaseHtType
CREATE TABLE [dbo].[cb_BaseHtType](
	[HtTypeGUID] [uniqueidentifier] NOT NULL,
	[HtTypeShortCode] [varchar](10) NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtTypeShortName] [varchar](40) NULL,
	[HtTypeName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
 CONSTRAINT [PK_BaseHtType_HtTypeGUID] PRIMARY KEY CLUSTERED 
(
	[HtTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Bid
CREATE TABLE [dbo].[cb_Bid](
	[BidGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[TaskGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[BidCode] [varchar](40) NULL,
	[BidName] [varchar](80) NULL,
	[BidClass] [varchar](16) NULL,
	[BidType] [varchar](16) NULL,
	[ApplyDate] [datetime] NULL,
	[Applicant] [varchar](20) NULL,
	[BidContent] [text] NULL,
	[JbDeptCode] [varchar](500) NULL,
	[Jbr] [varchar](20) NULL,
	[AdvanceDay] [money] NULL,
	[WorkDay] [money] NULL,
	[BeginDateJh] [datetime] NULL,
	[EndDateJh] [datetime] NULL,
	[BeginDateSj] [datetime] NULL,
	[EndDateSj] [datetime] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[LowestBidPrice] [money] NULL,
	[LowerBidPrice] [money] NULL,
	[MidBidPrice] [money] NULL,
	[HigherBidPrice] [money] NULL,
	[HighestBidPrice] [money] NULL,
	[LowestBidUnit] [varchar](100) NULL,
	[LowerBidUnit] [varchar](100) NULL,
	[MidBidUnit] [varchar](100) NULL,
	[HigherBidUnit] [varchar](100) NULL,
	[HighestBidUnit] [varchar](100) NULL,
	[ConfirmBidPrice] [money] NULL,
	[ConfirmBidUnit] [varchar](100) NULL,
	[BidUnitNum] [int] NULL,
	[BidDocNo] [varchar](20) NULL,
	[ProjGUID] [uniqueidentifier] NOT NULL,
	[WorkGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_Bid] PRIMARY KEY CLUSTERED 
(
	[BidGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Bid2Partner
CREATE TABLE [dbo].[cb_Bid2Partner](
	[Bid2PartnerGUID] [uniqueidentifier] NOT NULL,
	[BidGUID] [uniqueidentifier] NOT NULL,
	[PartnerGUID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_cb_Bid2Partner] PRIMARY KEY CLUSTERED 
(
	[Bid2PartnerGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Budget
CREATE TABLE [dbo].[cb_Budget](
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BudgetCode] [varchar](300) NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[BudgetAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[IsUseable] [tinyint] NULL,
	[FactCost] [money] NULL,
	[WorkGUID] [uniqueidentifier] NULL,
	[ExecuteDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[ExecuteDate] [datetime] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefName] [varchar](100) NULL,
	[IsZB] [tinyint] NULL,
	[CgType] [varchar](20) NULL,
	[CgCategoryGUID] [uniqueidentifier] NULL,
	[CgCategoryName] [varchar](100) NULL,
	[CZRelativelyDay] [int] NULL,
	[CZExecuteDate] [datetime] NULL,
	[TimeStampVer] [timestamp] NOT NULL,
	[SignupAmount] [decimal](18, 2) NULL,
	[AlterAmount] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [money] NOT NULL,
	[InputTaxBudgetAmount] [money] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[ExcludingTaxUsedAmount] [money] NOT NULL,
	[InputTaxUsedAmount] [money] NOT NULL,
	[UsedTaxRate] [decimal](18, 2) NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NULL,
	[ExcludingTaxFactCost] [money] NOT NULL,
	[InputTaxFactCost] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[InputTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxSignupAmount] [money] NOT NULL,
	[ExcludingTaxAlterAmount] [money] NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [money] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
 CONSTRAINT [PK_cb_Budget] PRIMARY KEY CLUSTERED 
(
	[BudgetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Budget0729
CREATE TABLE [dbo].[cb_Budget0729](
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BudgetCode] [varchar](300) NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[BudgetAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[IsUseable] [tinyint] NULL,
	[FactCost] [money] NULL,
	[WorkGUID] [uniqueidentifier] NULL,
	[ExecuteDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[ExecuteDate] [datetime] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefName] [varchar](100) NULL,
	[IsZB] [tinyint] NULL,
	[CgType] [varchar](20) NULL,
	[CgCategoryGUID] [uniqueidentifier] NULL,
	[CgCategoryName] [varchar](100) NULL,
	[CZRelativelyDay] [int] NULL,
	[CZExecuteDate] [datetime] NULL,
	[TimeStampVer] [timestamp] NOT NULL,
	[SignupAmount] [decimal](18, 2) NULL,
	[AlterAmount] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [money] NOT NULL,
	[InputTaxBudgetAmount] [money] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[ExcludingTaxUsedAmount] [money] NOT NULL,
	[InputTaxUsedAmount] [money] NOT NULL,
	[UsedTaxRate] [decimal](18, 2) NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NULL,
	[ExcludingTaxFactCost] [money] NOT NULL,
	[InputTaxFactCost] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[InputTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxSignupAmount] [money] NOT NULL,
	[ExcludingTaxAlterAmount] [money] NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [money] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Budget20220729
CREATE TABLE [dbo].[cb_Budget20220729](
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BudgetCode] [varchar](300) NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[BudgetAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[IsUseable] [tinyint] NULL,
	[FactCost] [money] NULL,
	[WorkGUID] [uniqueidentifier] NULL,
	[ExecuteDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[ExecuteDate] [datetime] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefName] [varchar](100) NULL,
	[IsZB] [tinyint] NULL,
	[CgType] [varchar](20) NULL,
	[CgCategoryGUID] [uniqueidentifier] NULL,
	[CgCategoryName] [varchar](100) NULL,
	[CZRelativelyDay] [int] NULL,
	[CZExecuteDate] [datetime] NULL,
	[TimeStampVer] [timestamp] NOT NULL,
	[SignupAmount] [decimal](18, 2) NULL,
	[AlterAmount] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [money] NOT NULL,
	[InputTaxBudgetAmount] [money] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[ExcludingTaxUsedAmount] [money] NOT NULL,
	[InputTaxUsedAmount] [money] NOT NULL,
	[UsedTaxRate] [decimal](18, 2) NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NULL,
	[ExcludingTaxFactCost] [money] NOT NULL,
	[InputTaxFactCost] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[InputTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxSignupAmount] [money] NOT NULL,
	[ExcludingTaxAlterAmount] [money] NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [money] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Budget20240323
CREATE TABLE [dbo].[cb_Budget20240323](
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BudgetCode] [varchar](300) NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[BudgetAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[IsUseable] [tinyint] NULL,
	[FactCost] [money] NULL,
	[WorkGUID] [uniqueidentifier] NULL,
	[ExecuteDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[ExecuteDate] [datetime] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefName] [varchar](100) NULL,
	[IsZB] [tinyint] NULL,
	[CgType] [varchar](20) NULL,
	[CgCategoryGUID] [uniqueidentifier] NULL,
	[CgCategoryName] [varchar](100) NULL,
	[CZRelativelyDay] [int] NULL,
	[CZExecuteDate] [datetime] NULL,
	[TimeStampVer] [timestamp] NOT NULL,
	[SignupAmount] [decimal](18, 2) NULL,
	[AlterAmount] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [money] NOT NULL,
	[InputTaxBudgetAmount] [money] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[ExcludingTaxUsedAmount] [money] NOT NULL,
	[InputTaxUsedAmount] [money] NOT NULL,
	[UsedTaxRate] [decimal](18, 2) NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NULL,
	[ExcludingTaxFactCost] [money] NOT NULL,
	[InputTaxFactCost] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[InputTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxSignupAmount] [money] NOT NULL,
	[ExcludingTaxAlterAmount] [money] NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [money] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Budget2Cost_Working
CREATE TABLE [dbo].[cb_Budget2Cost_Working](
	[Budget2CostGUID] [uniqueidentifier] NOT NULL,
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[PercentageOfAlter] [decimal](18, 2) NOT NULL,
	[SignupAmount] [decimal](18, 2) NOT NULL,
	[AlterAmount] [decimal](18, 2) NOT NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[ExcludingTaxSignupAmount] [decimal](18, 2) NOT NULL,
	[ExcludingTaxAlterAmount] [decimal](18, 2) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
 CONSTRAINT [PK_cb_Budget2Cost_Working] PRIMARY KEY CLUSTERED 
(
	[Budget2CostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BudgetAdjustLog
CREATE TABLE [dbo].[cb_BudgetAdjustLog](
	[BudgetAdjustLogGUID] [uniqueidentifier] NOT NULL,
	[WorkingBudgetGUID] [uniqueidentifier] NULL,
	[BudgetBillDetailGUID] [uniqueidentifier] NULL,
	[Content] [varchar](max) NULL,
	[JSONObject] [varchar](max) NULL,
	[CreateDate] [datetime] NOT NULL,
	[Type] [nvarchar](20) NULL,
	[ProjectList] [nvarchar](max) NULL,
 CONSTRAINT [PK_cb_BudgetAdjustLog] PRIMARY KEY CLUSTERED 
(
	[BudgetAdjustLogGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetBill
CREATE TABLE [dbo].[cb_BudgetBill](
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[Subject] [nvarchar](200) NULL,
	[UserGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NOT NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[Remark] [nvarchar](500) NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[Budget2CostWorkingProjectGuidList] [nvarchar](max) NULL,
 CONSTRAINT [PK_cb_BudgetBill] PRIMARY KEY CLUSTERED 
(
	[BudgetBillGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetBill20220707bak
CREATE TABLE [dbo].[cb_BudgetBill20220707bak](
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[Subject] [nvarchar](200) NULL,
	[UserGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NOT NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[Remark] [nvarchar](500) NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[Budget2CostWorkingProjectGuidList] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetBill20240823
CREATE TABLE [dbo].[cb_BudgetBill20240823](
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[Subject] [nvarchar](200) NULL,
	[UserGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NOT NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[Remark] [nvarchar](500) NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[Budget2CostWorkingProjectGuidList] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetBillDetail
CREATE TABLE [dbo].[cb_BudgetBillDetail](
	[BudgetBillDetailGUID] [uniqueidentifier] NOT NULL,
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[ModifyType] [int] NOT NULL,
	[PlanAmountBeforeAdjust] [decimal](18, 2) NOT NULL,
	[PlanAmountAfterAdjust] [decimal](18, 2) NOT NULL,
	[ExcludingTaxPlanAmountBeforeAdjust] [decimal](18, 2) NOT NULL,
	[ExcludingTaxPlanAmountAfterAdjust] [decimal](18, 2) NOT NULL,
 CONSTRAINT [PK_cb_BudgetBillDetail] PRIMARY KEY CLUSTERED 
(
	[BudgetBillDetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BudgetPlan
CREATE TABLE [dbo].[cb_BudgetPlan](
	[BudgetPlanGUID] [uniqueidentifier] NOT NULL,
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[JhfkDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRate] [money] NULL,
	[Remarks] [text] NULL,
	[WorkItemGuid] [uniqueidentifier] NULL,
	[WorkItem] [nvarchar](300) NULL,
 CONSTRAINT [PK_BudgetPlanGUID] PRIMARY KEY NONCLUSTERED 
(
	[BudgetPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetPlan_Working
CREATE TABLE [dbo].[cb_BudgetPlan_Working](
	[BudgetPlanGUID] [uniqueidentifier] NOT NULL,
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[JhfkDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRate] [money] NULL,
	[Remarks] [text] NULL,
	[WorkItemGuid] [uniqueidentifier] NULL,
	[WorkItem] [nvarchar](300) NULL,
 CONSTRAINT [PK_Working_BudgetPlanGUID] PRIMARY KEY NONCLUSTERED 
(
	[BudgetPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetUse
CREATE TABLE [dbo].[cb_BudgetUse](
	[BudgetUseGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[CfSource] [varchar](16) NULL,
	[CfTypeName] [varchar](16) NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[Remarks] [text] NULL,
	[Cost] [money] NULL,
	[BeforeCost] [money] NULL,
	[CfAmount] [money] NULL,
	[AfterCost] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[RefType] [varchar](16) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterCR] [money] NULL,
	[UpdateBudgetFlag] [tinyint] NULL,
	[BalanceMode] [tinyint] NULL,
	[NewBudgetName] [nvarchar](2000) NULL,
	[BalanceModeRemarks] [text] NULL,
	[HTBalanceGUID] [uniqueidentifier] NULL,
	[IsAbateZtCost] [tinyint] NULL,
	[IsApprove] [tinyint] NULL,
	[IsSGCR] [tinyint] NULL,
	[YgAlterAmountSP] [money] NULL,
	[QRAmount] [money] NULL,
	[LastQRAmount] [money] NULL,
	[IsQR] [tinyint] NULL,
	[CfAmountSP] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[HtYgAlterAmount] [money] NOT NULL,
	[YgAlterAdj] [money] NOT NULL,
	[SumAlterAmount] [money] NOT NULL,
	[QrAmountSP] [money] NOT NULL,
	[ApplyCfAmount] [money] NOT NULL,
	[AlterCfAmount] [money] NOT NULL,
	[QRHtYgAlterAmount] [money] NOT NULL,
	[QRYgAlterAdj] [money] NOT NULL,
	[QRSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[InputTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
	[InputTaxCfAmount] [money] NOT NULL,
	[TaxRateCfAmount] [money] NOT NULL,
	[TaxRateCfAmountName] [varchar](50) NOT NULL,
	[TaxRateBalanceMode] [money] NOT NULL,
	[TaxRateBalanceModeName] [varchar](50) NOT NULL,
	[ExcludingTaxYgAlterAdj] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxHtYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxAlterCfAmount] [money] NOT NULL,
	[ExcludingTaxAfterCost] [money] NOT NULL,
	[ExcludingTaxApplyCfAmount] [money] NOT NULL,
	[ExcludingTaxBeforeCost] [money] NOT NULL,
	[ExcludingTaxCost] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQrAmountSP] [money] NOT NULL,
	[ExcludingTaxQRHtYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxQRSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxQRYgAlterAdj] [money] NOT NULL,
	[ExcludingTaxYgAlterAmountSP] [money] NOT NULL,
	[ExcludingTaxYgAlterCR] [money] NOT NULL,
	[ExcludingTaxLastQRAmount] [money] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetUse20220729
CREATE TABLE [dbo].[cb_BudgetUse20220729](
	[BudgetUseGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[CfSource] [varchar](16) NULL,
	[CfTypeName] [varchar](16) NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[Remarks] [text] NULL,
	[Cost] [money] NULL,
	[BeforeCost] [money] NULL,
	[CfAmount] [money] NULL,
	[AfterCost] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[RefType] [varchar](16) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterCR] [money] NULL,
	[UpdateBudgetFlag] [tinyint] NULL,
	[BalanceMode] [tinyint] NULL,
	[NewBudgetName] [nvarchar](2000) NULL,
	[BalanceModeRemarks] [text] NULL,
	[HTBalanceGUID] [uniqueidentifier] NULL,
	[IsAbateZtCost] [tinyint] NULL,
	[IsApprove] [tinyint] NULL,
	[IsSGCR] [tinyint] NULL,
	[YgAlterAmountSP] [money] NULL,
	[QRAmount] [money] NULL,
	[LastQRAmount] [money] NULL,
	[IsQR] [tinyint] NULL,
	[CfAmountSP] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[HtYgAlterAmount] [money] NOT NULL,
	[YgAlterAdj] [money] NOT NULL,
	[SumAlterAmount] [money] NOT NULL,
	[QrAmountSP] [money] NOT NULL,
	[ApplyCfAmount] [money] NOT NULL,
	[AlterCfAmount] [money] NOT NULL,
	[QRHtYgAlterAmount] [money] NOT NULL,
	[QRYgAlterAdj] [money] NOT NULL,
	[QRSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[InputTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
	[InputTaxCfAmount] [money] NOT NULL,
	[TaxRateCfAmount] [money] NOT NULL,
	[TaxRateCfAmountName] [varchar](50) NOT NULL,
	[TaxRateBalanceMode] [money] NOT NULL,
	[TaxRateBalanceModeName] [varchar](50) NOT NULL,
	[ExcludingTaxYgAlterAdj] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxHtYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxAlterCfAmount] [money] NOT NULL,
	[ExcludingTaxAfterCost] [money] NOT NULL,
	[ExcludingTaxApplyCfAmount] [money] NOT NULL,
	[ExcludingTaxBeforeCost] [money] NOT NULL,
	[ExcludingTaxCost] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQrAmountSP] [money] NOT NULL,
	[ExcludingTaxQRHtYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxQRSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxQRYgAlterAdj] [money] NOT NULL,
	[ExcludingTaxYgAlterAmountSP] [money] NOT NULL,
	[ExcludingTaxYgAlterCR] [money] NOT NULL,
	[ExcludingTaxLastQRAmount] [money] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BudgetVersion
CREATE TABLE [dbo].[cb_BudgetVersion](
	[BudgetVersionGUID] [uniqueidentifier] NOT NULL,
	[CreateTime] [datetime] NOT NULL,
	[VersionName] [nvarchar](max) NOT NULL,
	[ApplyUserGUID] [uniqueidentifier] NOT NULL,
	[ApplyUserName] [nvarchar](max) NOT NULL,
	[IsBaseVersion] [bit] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[TreeJson] [varbinary](max) NOT NULL,
	[BudgetBillGUID] [uniqueidentifier] NULL,
	[TotalBudgetAmount] [decimal](18, 2) NULL,
 CONSTRAINT [PK_cb_BudgetVersion] PRIMARY KEY CLUSTERED 
(
	[BudgetVersionGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Budget_Executing
CREATE TABLE [dbo].[cb_Budget_Executing](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
 CONSTRAINT [PK_cb_Budget_Executing] PRIMARY KEY CLUSTERED 
(
	[ExecutingBudgetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Executing0729
CREATE TABLE [dbo].[cb_Budget_Executing0729](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Executing20220728
CREATE TABLE [dbo].[cb_Budget_Executing20220728](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Executing20220729
CREATE TABLE [dbo].[cb_Budget_Executing20220729](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Executing20240323
CREATE TABLE [dbo].[cb_Budget_Executing20240323](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Working
CREATE TABLE [dbo].[cb_Budget_Working](
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[ModifyType] [int] NOT NULL,
	[Remark] [varchar](500) NULL,
	[ApproveState] [varchar](10) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
	[IsModuleYr] [int] NOT NULL,
	[AdjustAmount] [money] NULL,
 CONSTRAINT [PK_cb_Budget_Working] PRIMARY KEY CLUSTERED 
(
	[WorkingBudgetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Working0729
CREATE TABLE [dbo].[cb_Budget_Working0729](
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[ModifyType] [int] NOT NULL,
	[Remark] [varchar](500) NULL,
	[ApproveState] [varchar](10) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
	[IsModuleYr] [int] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Working20220728
CREATE TABLE [dbo].[cb_Budget_Working20220728](
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[ModifyType] [int] NOT NULL,
	[Remark] [varchar](500) NULL,
	[ApproveState] [varchar](10) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
	[IsModuleYr] [int] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Budget_Working_UserLock
CREATE TABLE [dbo].[cb_Budget_Working_UserLock](
	[UserGUID] [uniqueidentifier] NOT NULL,
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_cb_Budget_Working_UserLock] PRIMARY KEY CLUSTERED 
(
	[WorkingBudgetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BulidCostFtRemark
CREATE TABLE [dbo].[cb_BulidCostFtRemark](
	[ProjGUID] [uniqueidentifier] NULL,
	[OutTargetGUID] [uniqueidentifier] NOT NULL,
	[CostCode] [varchar](100) NOT NULL,
	[FtRemark] [nvarchar](2000) NULL,
PRIMARY KEY CLUSTERED 
(
	[OutTargetGUID] ASC,
	[CostCode] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BulidCostFtRule
CREATE TABLE [dbo].[cb_BulidCostFtRule](
	[BulidCostFtRuleGUID] [uniqueidentifier] NOT NULL,
	[OutProjGUID] [uniqueidentifier] NULL,
	[OutTargetGUID] [uniqueidentifier] NULL,
	[OutCostCode] [varchar](100) NULL,
	[InProjGUID] [uniqueidentifier] NULL,
	[InProductGUID] [uniqueidentifier] NULL,
	[InCostCode] [varchar](100) NULL,
	[FtMode] [varchar](20) NULL,
	[FtRate] [decimal](27, 13) NULL,
	[FtAmount] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[BulidCostFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_BulidDtCostFtRemark
CREATE TABLE [dbo].[cb_BulidDtCostFtRemark](
	[ProjGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[FtRemark] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_BulidDtCostFtRule
CREATE TABLE [dbo].[cb_BulidDtCostFtRule](
	[BulidDtCostFtRuleGUID] [uniqueidentifier] NOT NULL,
	[OutProjGUID] [uniqueidentifier] NULL,
	[OutCostCode] [varchar](100) NULL,
	[OutRefGUID] [uniqueidentifier] NULL,
	[InProjGUID] [uniqueidentifier] NULL,
	[InProductGUID] [uniqueidentifier] NULL,
	[InCostCode] [varchar](100) NULL,
	[FtMode] [varchar](20) NULL,
	[FtRate] [decimal](27, 13) NULL,
	[FtAmount] [money] NULL,
	[FtSource] [varchar](20) NULL,
	[isFactFtObject] [tinyint] NULL,
	[isAutoFt] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[BulidDtCostFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CfDtl
CREATE TABLE [dbo].[cb_CfDtl](
	[CfDtlGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[CfSource] [varchar](16) NULL,
	[CfTypeName] [varchar](16) NULL,
	[CfTypeCode] [varchar](2) NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[CfAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefType] [varchar](16) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterCR] [money] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxYgAlterCR] [money] NOT NULL,
	[ExcludingTaxScale] [money] NOT NULL,
	[ExcludingTaxScaleDtl] [money] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CfGtProjectPlan
CREATE TABLE [dbo].[cb_CfGtProjectPlan](
	[ProjGUID] [uniqueidentifier] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CfRule
CREATE TABLE [dbo].[cb_CfRule](
	[CfRuleGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[Scale] [money] NULL,
	[ScaleDtl] [decimal](27, 13) NULL,
	[ExcludingTaxScaleDtl] [decimal](27, 13) NOT NULL,
	[ExcludingTaxScale] [money] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CfRuleCost
CREATE TABLE [dbo].[cb_CfRuleCost](
	[CfRuleCostGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[CostCode] [varchar](100) NULL,
	[FtRate] [money] NULL,
	[FtMode] [varchar](16) NULL,
 CONSTRAINT [PK_cb_CfRuleCost] PRIMARY KEY CLUSTERED 
(
	[CfRuleCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CfRuleProject
CREATE TABLE [dbo].[cb_CfRuleProject](
	[CfRuleProjectGUID] [uniqueidentifier] NOT NULL,
	[CfRuleCostGUID] [uniqueidentifier] NOT NULL,
	[ProjectCode] [varchar](100) NULL,
	[FtRate] [decimal](15, 10) NULL,
	[FtMode] [varchar](16) NULL,
	[FtAmount] [money] NULL,
	[ProjectGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormat
CREATE TABLE [dbo].[cb_CodeFormat](
	[CodeFormatGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL,
 CONSTRAINT [PK_cb_CodeFormat] PRIMARY KEY CLUSTERED 
(
	[CodeFormatGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormat20180927
CREATE TABLE [dbo].[cb_CodeFormat20180927](
	[CodeFormatGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormat20181009
CREATE TABLE [dbo].[cb_CodeFormat20181009](
	[CodeFormatGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormatTemplet
CREATE TABLE [dbo].[cb_CodeFormatTemplet](
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormatTemplet20180927
CREATE TABLE [dbo].[cb_CodeFormatTemplet20180927](
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CodeFormatTemplet20181009
CREATE TABLE [dbo].[cb_CodeFormatTemplet20181009](
	[CodeType] [varchar](20) NULL,
	[Num] [tinyint] NULL,
	[FieldNameChn] [varchar](30) NULL,
	[LevelLimit] [tinyint] NULL,
	[IfIncluded] [tinyint] NULL,
	[IfRestore] [tinyint] NULL,
	[Separator] [varchar](2) NULL,
	[ExampleData] [varchar](50) NULL,
	[ProjTypes] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Contract
CREATE TABLE [dbo].[cb_Contract](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL,
 CONSTRAINT [PK_cb_Contract_ContractGUID_0001] PRIMARY KEY NONCLUSTERED 
(
	[ContractGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract101701
CREATE TABLE [dbo].[cb_Contract101701](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract20190516
CREATE TABLE [dbo].[cb_Contract20190516](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract20241209
CREATE TABLE [dbo].[cb_Contract20241209](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract20241226
CREATE TABLE [dbo].[cb_Contract20241226](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract20250320
CREATE TABLE [dbo].[cb_Contract20250320](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Contract2DesignAlter
CREATE TABLE [dbo].[cb_Contract2DesignAlter](
	[Contract2DesignGuid] [uniqueidentifier] NOT NULL,
	[ContractGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[IsUseYgAlter] [tinyint] NULL,
	[IsQr] [tinyint] NOT NULL,
	[AlterOrderCode] [varchar](800) NULL,
PRIMARY KEY CLUSTERED 
(
	[Contract2DesignGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Contract2HTType
CREATE TABLE [dbo].[cb_Contract2HTType](
	[HtTypeGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Contract2List
CREATE TABLE [dbo].[cb_Contract2List](
	[Contract2ListGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ListTemplateGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[Contract2ListGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Contract2Tax
CREATE TABLE [dbo].[cb_Contract2Tax](
	[Contract2TaxGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxAmount] [money] NULL,
	[TaxAmount_Bz] [money] NULL,
	[TaxRate] [money] NULL,
	[ExcludingTaxAmount] [money] NULL,
	[ExcludingTaxAmount_Bz] [money] NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_Contract2Tax] PRIMARY KEY CLUSTERED 
(
	[Contract2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Contract2TaxDetail
CREATE TABLE [dbo].[cb_Contract2TaxDetail](
	[Contract2TaxDetailGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[TaxableCategory] [varchar](50) NULL,
	[TaxRate] [money] NULL,
	[Proj2LicenseGUID] [uniqueidentifier] NULL,
	[TaxDetailAmount] [money] NULL,
	[ExcludingTaxDetailAmount] [money] NULL,
	[InputTaxDetailAmount] [money] NULL,
	[CfPercent] [decimal](18, 2) NULL,
 CONSTRAINT [PK_cb_Contract2TaxDetail_Contract2TaxDetailGUID] PRIMARY KEY CLUSTERED 
(
	[Contract2TaxDetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Contract2Tax_Fddhs
CREATE TABLE [dbo].[cb_Contract2Tax_Fddhs](
	[Contract2TaxGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxAmount] [money] NULL,
	[TaxAmount_Bz] [money] NULL,
	[TaxRate] [money] NULL,
	[ExcludingTaxAmount] [money] NULL,
	[ExcludingTaxAmount_Bz] [money] NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_Contract2Tax_Fddhs] PRIMARY KEY CLUSTERED 
(
	[Contract2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractAction
CREATE TABLE [dbo].[cb_ContractAction](
	[IsView] [tinyint] NOT NULL,
	[IsEdit] [tinyint] NOT NULL,
	[IsDelete] [tinyint] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[OrgGuid] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[IsJbr] [tinyint] NOT NULL,
	[OrgType] [varchar](10) NOT NULL,
	[IsActionFlag] [tinyint] NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractAction2
CREATE TABLE [dbo].[cb_ContractAction2](
	[FkGUID] [uniqueidentifier] NOT NULL,
	[GXObject] [varchar](50) NOT NULL,
	[Type] [varchar](16) NOT NULL,
	[IsView] [tinyint] NOT NULL,
	[IsEdit] [tinyint] NOT NULL,
	[IsDelete] [tinyint] NOT NULL,
	[IsActionFlag] [tinyint] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractDeptUseInfo
CREATE TABLE [dbo].[cb_ContractDeptUseInfo](
	[ContractDeptUseInfo] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractExtend
CREATE TABLE [dbo].[cb_ContractExtend](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ContractBound] [varchar](500) NULL,
	[PayMode] [varchar](500) NULL,
	[QualityRequest] [varchar](500) NULL,
	[BXAssumpsit] [varchar](500) NULL,
	[IsAutoCreateHT] [tinyint] NULL,
	[PassPlanOrNot] [tinyint] NOT NULL,
	[FkDate] [datetime] NULL,
 CONSTRAINT [PK_cb_ContractExtend] PRIMARY KEY CLUSTERED 
(
	[ContractGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractListItem
CREATE TABLE [dbo].[cb_ContractListItem](
	[ContractListItemGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ListItemGUID] [uniqueidentifier] NULL,
	[Gcl] [money] NULL,
	[Rgf] [money] NULL,
	[Zcf] [money] NULL,
	[Shl] [money] NULL,
	[ZcfSum] [money] NULL,
	[Fcf] [money] NULL,
	[Glf] [money] NULL,
	[PriceBhs] [money] NULL,
	[Tax] [money] NULL,
	[Price] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ContractListItemGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractPG
CREATE TABLE [dbo].[cb_ContractPG](
	[ContractPGGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[SignRemarks] [text] NULL,
	[QualityRemarks] [text] NULL,
	[WorkPeriodRemarks] [text] NULL,
	[CostRemarks] [text] NULL,
	[AssistRemarks] [text] NULL,
	[OtherRemarks] [text] NULL,
	[QualityOpinion] [varchar](10) NULL,
	[WorkPeriodOpinion] [varchar](10) NULL,
	[AssistOpinion] [varchar](10) NULL,
	[QuoteOpinion] [varchar](10) NULL,
	[LocaleOpinion] [varchar](10) NULL,
	[TechnicOpinion] [varchar](10) NULL,
	[EconomicOpinion] [varchar](10) NULL,
	[GeneralOpinion] [varchar](10) NULL,
	[Score] [money] NULL,
	[Appraiser] [varchar](20) NULL,
	[AppraiseDate] [datetime] NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_ContractPG] PRIMARY KEY CLUSTERED 
(
	[ContractPGGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ContractPlan
CREATE TABLE [dbo].[cb_ContractPlan](
	[ContractPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractPlanName] [nvarchar](2000) NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[SourceType] [varchar](50) NULL,
	[ReferenceTimeType] [int] NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[SignupOffsetDays] [int] NULL,
	[IsBiddingAndPurchasing] [bit] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[Remark] [varchar](500) NULL,
	[ContractPlanTemplateGUID] [uniqueidentifier] NOT NULL,
	[CreateTime] [datetime] NULL,
 CONSTRAINT [PK_cb_ContractPlan] PRIMARY KEY CLUSTERED 
(
	[ContractPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractPlan2Cost
CREATE TABLE [dbo].[cb_ContractPlan2Cost](
	[ContractPlan2CostGUID] [uniqueidentifier] NOT NULL,
	[ContractPlanGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[PercentageOfAllocation] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[TaxGUID] [uniqueidentifier] NOT NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
 CONSTRAINT [PK_cb_ContractPlan2Cost] PRIMARY KEY CLUSTERED 
(
	[ContractPlan2CostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractPlanTemplate
CREATE TABLE [dbo].[cb_ContractPlanTemplate](
	[ContractPlanTemplateGUID] [uniqueidentifier] NOT NULL,
	[TemplateName] [nvarchar](200) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[CreateTime] [datetime] NULL,
	[Remark] [nvarchar](max) NULL,
 CONSTRAINT [PK_cb_ContractPlanTemplate] PRIMARY KEY CLUSTERED 
(
	[ContractPlanTemplateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ContractPrice
CREATE TABLE [dbo].[cb_ContractPrice](
	[ContractPriceGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[PriceName] [varchar](256) NULL,
	[PriceCode] [varchar](256) NULL,
	[ApplyAmount] [money] NULL,
	[AuditAmount] [money] NULL,
	[PriceType] [varchar](100) NULL,
	[PriceReason] [varchar](100) NULL,
	[PriceSort] [tinyint] NULL,
	[IsJczp] [tinyint] NULL,
	[Jbr] [varchar](50) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[JbDeptName] [varchar](50) NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ApplyDate] [datetime] NULL,
	[PriceContent] [varchar](2000) NULL,
	[ApproveState] [varchar](10) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractPriceCsMaterial
CREATE TABLE [dbo].[cb_ContractPriceCsMaterial](
	[ContractPriceCsMaterialGUID] [uniqueidentifier] NULL,
	[ContractPriceGUID] [uniqueidentifier] NULL,
	[CsName] [varchar](256) NULL,
	[Brand] [varchar](256) NULL,
	[Specification] [varchar](256) NULL,
	[Factory] [varchar](256) NULL,
	[UsePart] [varchar](256) NULL,
	[Unity] [varchar](256) NULL,
	[Quantities] [varchar](256) NULL,
	[CsPrice] [money] NULL,
	[TaxRate] [money] NULL,
	[CsTotalPrice] [money] NULL,
	[AuditCsPrice] [money] NULL,
	[AuditTaxRate] [money] NULL,
	[AuditCsTotalPrice] [money] NULL,
	[Remarks] [varchar](2000) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractPriceZdMaterial
CREATE TABLE [dbo].[cb_ContractPriceZdMaterial](
	[ContractPriceZdMaterialGUID] [uniqueidentifier] NULL,
	[ContractPriceGUID] [uniqueidentifier] NULL,
	[ListProjName] [varchar](256) NULL,
	[ListProjFeature] [varchar](256) NULL,
	[UsePart] [varchar](256) NULL,
	[Unity] [varchar](256) NULL,
	[Quantities] [varchar](256) NULL,
	[MainMaterial] [money] NULL,
	[MainMaterialLoss] [money] NULL,
	[AuxiliaryMaterial] [money] NULL,
	[ManualWork] [money] NULL,
	[Machine] [money] NULL,
	[ManageTax] [money] NULL,
	[TaxRate] [money] NULL,
	[ZhPrice] [money] NULL,
	[AuditMainMaterial] [money] NULL,
	[AuditMainMaterialLoss] [money] NULL,
	[AuditAuxiliaryMaterial] [money] NULL,
	[AuditManualWork] [money] NULL,
	[AuditMachine] [money] NULL,
	[AuditManageTax] [money] NULL,
	[AuditTaxRate] [money] NULL,
	[AuditZhPrice] [money] NULL,
	[Remarks] [varchar](2000) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractProductBudget
CREATE TABLE [dbo].[cb_ContractProductBudget](
	[ContractProductBudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ProductTypeGUID] [uniqueidentifier] NULL,
	[PlanPrice] [money] NULL,
	[PlanQty] [money] NULL,
	[PlanAmount] [money] NULL,
	[LatestPrice] [money] NULL,
	[UnreceivedAmount] [money] NULL,
	[ReceivedQtyAll] [money] NULL,
	[ReceivedAmountAll] [money] NULL,
	[DtAmount] [money] NULL,
	[Remarks] [text] NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
 CONSTRAINT [PK_cb_ContractProductBudget] PRIMARY KEY CLUSTERED 
(
	[ContractProductBudgetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ContractProj
CREATE TABLE [dbo].[cb_ContractProj](
	[ContractGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractProj20180719
CREATE TABLE [dbo].[cb_ContractProj20180719](
	[ContractGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ContractTemplateMapping
CREATE TABLE [dbo].[cb_ContractTemplateMapping](
	[ContractTemplateMappingGUID] [uniqueidentifier] NOT NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[TemplateVersion] [varchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[ContractTemplateMappingGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ContractYgAlter
CREATE TABLE [dbo].[cb_ContractYgAlter](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[YgAlterAmount_Bz] [money] NULL,
	[YgAlterRate] [money] NULL,
	[ExcludingTaxYgAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxYgAlterRate] [decimal](18, 2) NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Cost
CREATE TABLE [dbo].[cb_Cost](
	[CostGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IfEndCost] [tinyint] NULL,
	[FtMode] [varchar](16) NULL,
	[ProductFtMode] [varchar](16) NULL,
	[IfEstimateChecked] [tinyint] NULL,
	[IfEstimateEmended] [tinyint] NULL,
	[IfTargetChecked] [tinyint] NULL,
	[IfTargetEmended] [tinyint] NULL,
	[CbjsState] [varchar](10) NULL,
	[Remarks] [varchar](max) NULL,
	[EstimateCost] [money] NULL,
	[TargetCost] [money] NULL,
	[AdjustCost] [money] NULL,
	[CbjsAdjustCost] [money] NULL,
	[DtCost] [money] NULL,
	[YfsCost] [money] NULL,
	[DfsCost] [money] NULL,
	[YfsCostYc] [money] NULL,
	[WjsQyAmount] [money] NULL,
	[WjsDesignAlter] [money] NULL,
	[WjsLocaleAlter] [money] NULL,
	[WjsOtherAlter] [money] NULL,
	[WjsBalanceAdjust] [money] NULL,
	[WjsHtxCost] [money] NULL,
	[YjsQyAmount] [money] NULL,
	[YjsDesignAlter] [money] NULL,
	[YjsLocaleAlter] [money] NULL,
	[YjsOtherAlter] [money] NULL,
	[YjsBalanceAdjust] [money] NULL,
	[YjsHtxCost] [money] NULL,
	[SumALterCost] [money] NULL,
	[SumHtxCost] [money] NULL,
	[WjsHtycCost] [money] NULL,
	[YjsHtycCost] [money] NULL,
	[SumHtycCost] [money] NULL,
	[WjsItemCost] [money] NULL,
	[YjsItemCost] [money] NULL,
	[SumItemCost] [money] NULL,
	[FhtxCost] [money] NULL,
	[FactCost] [money] NULL,
	[PayCost] [money] NULL,
	[FinanceHskmName] [varchar](40) NULL,
	[IsTzgs] [tinyint] NULL,
	[IfEstimate] [tinyint] NULL,
	[ProjectType] [varchar](10) NULL,
	[TargetTFAmount] [money] NULL,
	[IsGT] [tinyint] NULL,
	[LayoutSpare] [money] NULL,
	[DfsBudget] [money] NULL,
	[BudgetCost] [money] NULL,
	[YGAlter] [money] NULL,
	[CostType] [varchar](20) NULL,
	[IsBudgetItem] [tinyint] NULL,
	[IsEndBudgetItem] [tinyint] NULL,
	[ZTCost] [money] NULL,
	[CostKind] [varchar](50) NULL,
	[CostCategory] [varchar](50) NULL,
	[IsBigCost] [tinyint] NULL,
	[IsBindKeyNode] [int] NULL,
	[ExcludingTaxAdjustCost] [money] NOT NULL,
	[ExcludingTaxBudgetCost] [money] NOT NULL,
	[ExcludingTaxCbjsAdjustCost] [money] NOT NULL,
	[ExcludingTaxDfsBudget] [money] NOT NULL,
	[ExcludingTaxDfsCost] [money] NOT NULL,
	[ExcludingTaxDtCost] [money] NOT NULL,
	[ExcludingTaxEstimateCost] [money] NOT NULL,
	[ExcludingTaxFactCost] [money] NULL,
	[ExcludingTaxFhtxCost] [money] NOT NULL,
	[ExcludingTaxLayoutSpare] [money] NOT NULL,
	[ExcludingTaxPayCost] [money] NOT NULL,
	[ExcludingTaxSumALterCost] [money] NOT NULL,
	[ExcludingTaxSumHtxCost] [money] NOT NULL,
	[ExcludingTaxSumHtycCost] [money] NOT NULL,
	[ExcludingTaxSumItemCost] [money] NOT NULL,
	[ExcludingTaxTargetCost] [money] NOT NULL,
	[ExcludingTaxTargetTFAmount] [money] NOT NULL,
	[ExcludingTaxWjsBalanceAdjust] [money] NOT NULL,
	[ExcludingTaxWjsDesignAlter] [money] NOT NULL,
	[ExcludingTaxWjsHtxCost] [money] NOT NULL,
	[ExcludingTaxWjsHtycCost] [money] NOT NULL,
	[ExcludingTaxWjsItemCost] [money] NOT NULL,
	[ExcludingTaxWjsLocaleAlter] [money] NOT NULL,
	[ExcludingTaxWjsOtherAlter] [money] NOT NULL,
	[ExcludingTaxWjsQyAmount] [money] NOT NULL,
	[ExcludingTaxYfsCost] [money] NOT NULL,
	[ExcludingTaxYfsCostYc] [money] NOT NULL,
	[ExcludingTaxYGAlter] [money] NOT NULL,
	[ExcludingTaxYjsBalanceAdjust] [money] NOT NULL,
	[ExcludingTaxYjsDesignAlter] [money] NOT NULL,
	[ExcludingTaxYjsHtxCost] [money] NOT NULL,
	[ExcludingTaxYjsHtycCost] [money] NOT NULL,
	[ExcludingTaxYjsItemCost] [money] NOT NULL,
	[ExcludingTaxYjsLocaleAlter] [money] NOT NULL,
	[ExcludingTaxYjsOtherAlter] [money] NOT NULL,
	[ExcludingTaxYjsQyAmount] [money] NOT NULL,
	[ExcludingTaxZTCost] [money] NOT NULL,
	[InputTaxTargetCost] [money] NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[InputTaxAdjustCost] [money] NOT NULL,
	[InputTaxYfsCost] [money] NOT NULL,
	[InputTaxZTCost] [money] NOT NULL,
	[InputTaxDfsBudget] [money] NOT NULL,
	[InputTaxYGAlter] [money] NOT NULL,
	[InputTaxLayoutSpare] [money] NOT NULL,
	[NCCostCode] [varchar](200) NULL,
	[TCPayAmount] [money] NULL,
	[TCNoTaxPayAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CostControlSet
CREATE TABLE [dbo].[cb_CostControlSet](
	[CostControlSetGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ControlType] [varchar](10) NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[noCostGUID] [uniqueidentifier] NULL,
	[WarnRate] [money] NULL,
	[WarnAmount] [money] NULL,
	[CompelRate] [money] NULL,
	[CompelAmount] [money] NULL,
	[IsControl] [tinyint] NULL,
	[Buguid] [uniqueidentifier] NULL,
	[NoCostName] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CostControlinform
CREATE TABLE [dbo].[cb_CostControlinform](
	[CostControlinformGUID] [uniqueidentifier] NULL,
	[ControlType] [varchar](10) NULL,
	[IfYjtz] [tinyint] NULL,
	[IfQktz] [tinyint] NULL,
	[StateGUID] [uniqueidentifier] NULL,
	[Buguid] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CostDtl
CREATE TABLE [dbo].[cb_CostDtl](
	[CostDtlGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[CostType] [varchar](10) NULL,
	[AdjustType] [varchar](10) NULL,
	[IfHistory] [tinyint] NULL,
	[CalcMethod] [varchar](20) NULL,
	[Qty] [money] NULL,
	[Unit] [varchar](16) NULL,
	[Price] [money] NULL,
	[Amount] [money] NULL,
	[Remarks] [text] NULL,
	[EmendAction] [varchar](16) NULL,
	[EmendDate] [datetime] NULL,
	[LastPrice] [money] NULL,
	[LastQty] [money] NULL,
	[LastAmount] [money] NULL,
 CONSTRAINT [PK_cb_CostDtl] PRIMARY KEY CLUSTERED 
(
	[CostDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CostDtlHistory
CREATE TABLE [dbo].[cb_CostDtlHistory](
	[CostPlan2ProjectGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[CostType] [varchar](10) NULL,
	[AdjustType] [varchar](10) NULL,
	[CalcMethod] [varchar](20) NULL,
	[Qty] [money] NULL,
	[Unit] [varchar](16) NULL,
	[Price] [money] NULL,
	[Amount] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CostHistory
CREATE TABLE [dbo].[cb_CostHistory](
	[CostPlan2ProjectGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[Amount] [money] NULL,
	[TargetCost] [money] NULL,
	[BuildPrice] [money] NULL,
	[SalePrice] [money] NULL,
	[AdjustCost] [money] NOT NULL,
	[ConstraintDate] [datetime] NULL,
	[ConstraintType] [tinyint] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CostNoControl
CREATE TABLE [dbo].[cb_CostNoControl](
	[CostControlSetGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CostPlan
CREATE TABLE [dbo].[cb_CostPlan](
	[CostPlanGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[CostPlanName] [varchar](80) NULL,
	[CostType] [varchar](12) NULL,
	[SaveDate] [datetime] NULL,
	[SavedBy] [varchar](20) NULL,
	[Remarks] [text] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
 CONSTRAINT [PK_cb_CostPlan] PRIMARY KEY CLUSTERED 
(
	[CostPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_CostPlan2Project
CREATE TABLE [dbo].[cb_CostPlan2Project](
	[CostPlan2ProjectGUID] [uniqueidentifier] NOT NULL,
	[CostPlanGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_cb_CostPlan2Project] PRIMARY KEY CLUSTERED 
(
	[CostPlan2ProjectGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CostProjectftMode
CREATE TABLE [dbo].[cb_CostProjectftMode](
	[CostProjectftModeGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[ProjectFtMode] [varchar](16) NULL,
 CONSTRAINT [PK_cb_CostProjectftMode] PRIMARY KEY CLUSTERED 
(
	[CostProjectftModeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CostStationRights
CREATE TABLE [dbo].[cb_CostStationRights](
	[CostStationRightsGUID] [uniqueidentifier] NOT NULL,
	[StationGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[DepGUID] [uniqueidentifier] NULL,
	[ProjGuid] [uniqueidentifier] NULL,
	[CostType] [varchar](10) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CostZrcbSet
CREATE TABLE [dbo].[cb_CostZrcbSet](
	[CostZrcbSetGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ParamGUID] [uniqueidentifier] NULL,
	[DeptRate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[CostZrcbSetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Currency
CREATE TABLE [dbo].[cb_Currency](
	[CurrencyGUID] [uniqueidentifier] NOT NULL,
	[CurrencyCode] [varchar](10) NULL,
	[CurrencyName] [varchar](10) NULL,
	[Rate] [money] NULL,
	[IfSys] [tinyint] NULL,
 CONSTRAINT [PK_cb_Currency] PRIMARY KEY CLUSTERED 
(
	[CurrencyGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkCwztSet
CREATE TABLE [dbo].[cb_CwjkCwztSet](
	[CwztGUID] [uniqueidentifier] NULL,
	[CwztName] [varchar](50) NULL,
	[Application] [varchar](20) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[isNullProj] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkHsxmMap
CREATE TABLE [dbo].[cb_CwjkHsxmMap](
	[CwztGUID] [uniqueidentifier] NULL,
	[HsTypeGUID] [uniqueidentifier] NULL,
	[HsxmSort] [varchar](80) NULL,
	[HsxmCode] [varchar](80) NULL,
	[HsxmName] [varchar](80) NULL,
	[OperObject] [varchar](20) NULL,
	[ObjectGUID] [uniqueidentifier] NULL,
	[ThisTabGUID] [uniqueidentifier] NULL,
	[ObjectName] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkHsxmSet
CREATE TABLE [dbo].[cb_CwjkHsxmSet](
	[CwztGUID] [uniqueidentifier] NULL,
	[OperObject] [varchar](20) NULL,
	[HsTypeGUID] [uniqueidentifier] NULL,
	[ThisTabGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkItemRuleSet
CREATE TABLE [dbo].[cb_CwjkItemRuleSet](
	[CwztGUID] [uniqueidentifier] NULL,
	[ItemType] [varchar](20) NULL,
	[ItemName] [varchar](50) NULL,
	[BeforJzKmTypeGUID] [uniqueidentifier] NULL,
	[AfterJzKmTypeGUID] [uniqueidentifier] NULL,
	[RuleGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkKjkm2Hsxm
CREATE TABLE [dbo].[cb_CwjkKjkm2Hsxm](
	[KmTypeGUID] [uniqueidentifier] NULL,
	[HsTypeGUID] [uniqueidentifier] NULL,
	[ThisTabGUID] [uniqueidentifier] NULL,
	[CwztGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkKjkmMap
CREATE TABLE [dbo].[cb_CwjkKjkmMap](
	[CwztGUID] [uniqueidentifier] NULL,
	[KmTypeGUID] [uniqueidentifier] NULL,
	[KmCode] [varchar](50) NULL,
	[KmName] [varchar](80) NULL,
	[OperObject] [varchar](20) NULL,
	[ObjectGUID] [uniqueidentifier] NULL,
	[ThisTabGUID] [uniqueidentifier] NULL,
	[Bz] [varchar](8) NULL,
	[ObjectName] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkKjkmSet
CREATE TABLE [dbo].[cb_CwjkKjkmSet](
	[CwztGUID] [uniqueidentifier] NULL,
	[KmTypeGUID] [uniqueidentifier] NULL,
	[Bz] [varchar](10) NULL,
	[OperObject] [varchar](20) NULL,
	[Income] [varchar](10) NULL,
	[Expend] [varchar](10) NULL,
	[ThisTabGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkParamSet
CREATE TABLE [dbo].[cb_CwjkParamSet](
	[ParamGUID] [uniqueidentifier] NULL,
	[ParamType] [varchar](20) NULL,
	[ParamName] [varchar](50) NULL,
	[Sequence] [varchar](8) NULL,
	[YeDirection] [varchar](10) NULL,
	[application] [varchar](50) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkPzzSet
CREATE TABLE [dbo].[cb_CwjkPzzSet](
	[BUGUID] [uniqueidentifier] NULL,
	[CwztGUID] [uniqueidentifier] NULL,
	[Pzz] [varchar](10) NULL,
	[OperType] [varchar](10) NULL,
	[GetForm] [varchar](10) NULL,
	[PzzGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_CwjkVoucher
CREATE TABLE [dbo].[cb_CwjkVoucher](
	[CwztGUID] [uniqueidentifier] NULL,
	[fDate] [datetime] NULL,
	[fTransDate] [datetime] NULL,
	[fPeriod] [int] NULL,
	[fGroup] [varchar](10) NULL,
	[fNum] [int] NULL,
	[fEntryID] [int] NULL,
	[fExp] [varchar](400) NULL,
	[fAcctID] [varchar](40) NULL,
	[fClsName1] [varchar](80) NULL,
	[fObjID1] [varchar](80) NULL,
	[fObjName1] [varchar](80) NULL,
	[fClsName2] [varchar](80) NULL,
	[fObjID2] [varchar](80) NULL,
	[fObjName2] [varchar](80) NULL,
	[fClsName3] [varchar](80) NULL,
	[fObjID3] [varchar](80) NULL,
	[fObjName3] [varchar](80) NULL,
	[fClsName4] [varchar](80) NULL,
	[fObjID4] [varchar](80) NULL,
	[fObjName4] [varchar](80) NULL,
	[fClsName5] [varchar](80) NULL,
	[fObjID5] [varchar](80) NULL,
	[fObjName5] [varchar](80) NULL,
	[fClsName6] [varchar](80) NULL,
	[fObjID6] [varchar](80) NULL,
	[fObjName6] [varchar](80) NULL,
	[fClsName7] [varchar](80) NULL,
	[fObjID7] [varchar](80) NULL,
	[fObjName7] [varchar](80) NULL,
	[fClsName8] [varchar](80) NULL,
	[fObjID8] [varchar](80) NULL,
	[fObjName8] [varchar](80) NULL,
	[fTransID] [varchar](80) NULL,
	[fCyID] [varchar](10) NULL,
	[fExchRate] [money] NULL,
	[fDC] [varchar](1) NULL,
	[fFcyAmt] [money] NULL,
	[fQty] [money] NULL,
	[fPrice] [money] NULL,
	[fDebit] [money] NULL,
	[fCredit] [money] NULL,
	[fSettlCode] [varchar](80) NULL,
	[fSettleNo] [varchar](40) NULL,
	[fPrepare] [varchar](80) NULL,
	[fPoster] [varchar](80) NULL,
	[fChecker] [varchar](80) NULL,
	[fAttchment] [int] NULL,
	[fPosted] [tinyint] NULL,
	[fModule] [varchar](10) NULL,
	[fDeleted] [tinyint] NULL,
	[fSerialNo] [int] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[SerialGUID] [uniqueidentifier] NULL,
	[VouchGUID] [uniqueidentifier] NULL,
	[ThisTabGUID] [uniqueidentifier] NULL,
	[CreatedOn] [datetime] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_DTCBProfitRate
CREATE TABLE [dbo].[cb_DTCBProfitRate](
	[CBProfitRateGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[PlanSaleAmount] [money] NULL,
	[TargetCost] [money] NULL,
	[PubBuildFtCost] [money] NULL,
	[SaleTaxExtra] [money] NULL,
	[SaleTaxExtraRate] [money] NULL,
	[LandIncrementTax] [money] NULL,
	[BusinessTax] [money] NULL,
	[BusinessTaxRate] [money] NULL,
	[PlanProfit] [money] NULL,
	[PlanProfitRate] [money] NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[IfPhotoStore] [bit] NULL,
 CONSTRAINT [PK_CB_DTCBPROFITRATE] PRIMARY KEY CLUSTERED 
(
	[CBProfitRateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DTCostRecollect
CREATE TABLE [dbo].[cb_DTCostRecollect](
	[RecollectGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[ProjectName] [varchar](400) NULL,
	[RecollectDate] [datetime] NULL,
	[LastestCostVersion] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[CreateUserGUID] [uniqueidentifier] NULL,
	[CreateUserName] [varchar](20) NULL,
	[CurVersion] [varchar](100) NULL,
	[LastVersion] [varchar](100) NULL,
	[Remarks] [text] NULL,
	[IsEndVersionByMonth] [tinyint] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DeptCostUseDtl
CREATE TABLE [dbo].[cb_DeptCostUseDtl](
	[DeptCostUseDtlGUID] [uniqueidentifier] NOT NULL,
	[BusinessType] [varchar](50) NULL,
	[BusinessGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ProceedingGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[ZrrGUID] [uniqueidentifier] NULL,
	[ZrrName] [varchar](50) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[DeptName] [varchar](500) NULL,
	[FtAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[Year] [int] NULL,
	[Month] [tinyint] NULL,
	[CostFlag] [tinyint] NULL,
	[EntryCostGUID] [uniqueidentifier] NULL,
	[EntryCostName] [varchar](500) NULL,
	[EntryCostCode] [varchar](100) NULL,
 CONSTRAINT [pk_cb_DeptCostUseDtl_DeptCostUseDtlGUID] PRIMARY KEY CLUSTERED 
(
	[DeptCostUseDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DesignAlter
CREATE TABLE [dbo].[cb_DesignAlter](
	[DesignAlterGuid] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterName] [varchar](120) NULL,
	[AlterType] [varchar](20) NULL,
	[AlterReason] [varchar](30) NULL,
	[Remarks] [text] NULL,
	[ProjectInfo] [varchar](200) NULL,
	[ReportState] [varchar](30) NULL,
	[ApplyAmount] [money] NOT NULL,
	[DesignAlterAmount] [money] NOT NULL,
	[ApplyAmount_Bz] [money] NOT NULL,
	[DesignAlterAmount_Bz] [money] NOT NULL,
	[Bz] [varchar](20) NULL,
	[Rate] [money] NOT NULL,
	[ContractInfo] [varchar](2000) NULL,
	[Jbr] [varchar](30) NULL,
	[JbrGuid] [uniqueidentifier] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[JbDept] [varchar](30) NULL,
	[ReportDate] [datetime] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[AlterLocation] [varchar](50) NULL,
	[ToPaperCode] [varchar](50) NULL,
	[CostBearedCompanyGuid] [uniqueidentifier] NULL,
	[CostBearedCompanyName] [varchar](200) NULL,
	[IsAffectSalePromise] [tinyint] NULL,
	[IsReturnWork] [tinyint] NULL,
	[CorrelativeDesignAlterGuid] [uniqueidentifier] NULL,
	[CorrelativeDesignAlterName] [varchar](200) NULL,
	[ReturnWorkAmount] [money] NOT NULL,
	[ReturnReason] [text] NULL,
	[ConnectFileGuid] [uniqueidentifier] NULL,
	[ApproveStatus] [varchar](20) NULL,
	[ApprovePassDate] [datetime] NULL,
	[ContractBudgetInfo] [varchar](500) NULL,
	[MidSubjectInfo] [varchar](500) NULL,
	[InvaildStatus] [varchar](10) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildReason] [varchar](500) NULL,
	[ContractGUIDList] [varchar](2000) NULL,
	[ProjGuidList] [varchar](2000) NULL,
	[DesignAlterCodeFormat] [varchar](800) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjCodeList] [varchar](2000) NULL,
	[ContractCodeList] [varchar](2000) NULL,
	[AlterTypeCode] [varchar](200) NOT NULL,
	[HTAlterNameList] [varchar](2000) NOT NULL,
	[HTAlterGuidList] [varchar](2000) NOT NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxDesignAlterAmount] [money] NULL,
	[InputTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxDesignAlterAmount] [money] NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxReturnWorkAmount] [money] NOT NULL,
	[WgqrPension] [varchar](50) NULL,
	[WgqrPensionGUID] [uniqueidentifier] NULL,
	[DesignAlterAmountIsChanged] [tinyint] NULL,
	[ExcludingCbAmount] [money] NULL,
	[EstimatedAmount] [money] NULL,
	[InvalidCostAmount] [money] NULL,
	[IsYxys] [tinyint] NULL,
	[IsYcz] [tinyint] NULL,
	[IsJj] [tinyint] NULL,
	[IsGbsjfa] [tinyint] NULL,
	[IsYxgn] [tinyint] NULL,
	[IsCltz] [tinyint] NULL,
	[IsHb] [tinyint] NULL,
	[IsYyTc] [tinyint] NULL,
	[IsYxhtFt] [tinyint] NULL,
	[ImplementationTime] [datetime] NULL,
	[IsKzProgressPayment] [datetime] NULL,
	[JrWxCbYgAmount] [money] NULL,
	[IsYbGc] [int] NOT NULL,
	[JrWxCbAuditAmount] [money] NULL,
	[InvalidCostAuditAmount] [money] NULL,
	[CongFuFSJinE] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[DesignAlterGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DesignAlter2021121601
CREATE TABLE [dbo].[cb_DesignAlter2021121601](
	[DesignAlterGuid] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterName] [varchar](120) NULL,
	[AlterType] [varchar](20) NULL,
	[AlterReason] [varchar](30) NULL,
	[Remarks] [text] NULL,
	[ProjectInfo] [varchar](200) NULL,
	[ReportState] [varchar](30) NULL,
	[ApplyAmount] [money] NOT NULL,
	[DesignAlterAmount] [money] NOT NULL,
	[ApplyAmount_Bz] [money] NOT NULL,
	[DesignAlterAmount_Bz] [money] NOT NULL,
	[Bz] [varchar](20) NULL,
	[Rate] [money] NOT NULL,
	[ContractInfo] [varchar](2000) NULL,
	[Jbr] [varchar](30) NULL,
	[JbrGuid] [uniqueidentifier] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[JbDept] [varchar](30) NULL,
	[ReportDate] [datetime] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[AlterLocation] [varchar](50) NULL,
	[ToPaperCode] [varchar](50) NULL,
	[CostBearedCompanyGuid] [uniqueidentifier] NULL,
	[CostBearedCompanyName] [varchar](200) NULL,
	[IsAffectSalePromise] [tinyint] NULL,
	[IsReturnWork] [tinyint] NULL,
	[CorrelativeDesignAlterGuid] [uniqueidentifier] NULL,
	[CorrelativeDesignAlterName] [varchar](200) NULL,
	[ReturnWorkAmount] [money] NOT NULL,
	[ReturnReason] [text] NULL,
	[ConnectFileGuid] [uniqueidentifier] NULL,
	[ApproveStatus] [varchar](20) NULL,
	[ApprovePassDate] [datetime] NULL,
	[ContractBudgetInfo] [varchar](500) NULL,
	[MidSubjectInfo] [varchar](500) NULL,
	[InvaildStatus] [varchar](10) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildReason] [varchar](500) NULL,
	[ContractGUIDList] [varchar](2000) NULL,
	[ProjGuidList] [varchar](2000) NULL,
	[DesignAlterCodeFormat] [varchar](800) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjCodeList] [varchar](2000) NULL,
	[ContractCodeList] [varchar](2000) NULL,
	[AlterTypeCode] [varchar](200) NOT NULL,
	[HTAlterNameList] [varchar](2000) NOT NULL,
	[HTAlterGuidList] [varchar](2000) NOT NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxDesignAlterAmount] [money] NULL,
	[InputTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxDesignAlterAmount] [money] NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxReturnWorkAmount] [money] NOT NULL,
	[WgqrPension] [varchar](50) NULL,
	[WgqrPensionGUID] [uniqueidentifier] NULL,
	[DesignAlterAmountIsChanged] [tinyint] NULL,
	[ExcludingCbAmount] [money] NULL,
	[EstimatedAmount] [money] NULL,
	[InvalidCostAmount] [money] NULL,
	[IsYxys] [tinyint] NULL,
	[IsYcz] [tinyint] NULL,
	[IsJj] [tinyint] NULL,
	[IsGbsjfa] [tinyint] NULL,
	[IsYxgn] [tinyint] NULL,
	[IsCltz] [tinyint] NULL,
	[IsHb] [tinyint] NULL,
	[IsYyTc] [tinyint] NULL,
	[IsYxhtFt] [tinyint] NULL,
	[ImplementationTime] [datetime] NULL,
	[IsKzProgressPayment] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DesignAlter20220316
CREATE TABLE [dbo].[cb_DesignAlter20220316](
	[DesignAlterGuid] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterName] [varchar](120) NULL,
	[AlterType] [varchar](20) NULL,
	[AlterReason] [varchar](30) NULL,
	[Remarks] [text] NULL,
	[ProjectInfo] [varchar](200) NULL,
	[ReportState] [varchar](30) NULL,
	[ApplyAmount] [money] NOT NULL,
	[DesignAlterAmount] [money] NOT NULL,
	[ApplyAmount_Bz] [money] NOT NULL,
	[DesignAlterAmount_Bz] [money] NOT NULL,
	[Bz] [varchar](20) NULL,
	[Rate] [money] NOT NULL,
	[ContractInfo] [varchar](2000) NULL,
	[Jbr] [varchar](30) NULL,
	[JbrGuid] [uniqueidentifier] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[JbDept] [varchar](30) NULL,
	[ReportDate] [datetime] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[AlterLocation] [varchar](50) NULL,
	[ToPaperCode] [varchar](50) NULL,
	[CostBearedCompanyGuid] [uniqueidentifier] NULL,
	[CostBearedCompanyName] [varchar](200) NULL,
	[IsAffectSalePromise] [tinyint] NULL,
	[IsReturnWork] [tinyint] NULL,
	[CorrelativeDesignAlterGuid] [uniqueidentifier] NULL,
	[CorrelativeDesignAlterName] [varchar](200) NULL,
	[ReturnWorkAmount] [money] NOT NULL,
	[ReturnReason] [text] NULL,
	[ConnectFileGuid] [uniqueidentifier] NULL,
	[ApproveStatus] [varchar](20) NULL,
	[ApprovePassDate] [datetime] NULL,
	[ContractBudgetInfo] [varchar](500) NULL,
	[MidSubjectInfo] [varchar](500) NULL,
	[InvaildStatus] [varchar](10) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildReason] [varchar](500) NULL,
	[ContractGUIDList] [varchar](2000) NULL,
	[ProjGuidList] [varchar](2000) NULL,
	[DesignAlterCodeFormat] [varchar](800) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjCodeList] [varchar](2000) NULL,
	[ContractCodeList] [varchar](2000) NULL,
	[AlterTypeCode] [varchar](200) NOT NULL,
	[HTAlterNameList] [varchar](2000) NOT NULL,
	[HTAlterGuidList] [varchar](2000) NOT NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxDesignAlterAmount] [money] NULL,
	[InputTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxDesignAlterAmount] [money] NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxReturnWorkAmount] [money] NOT NULL,
	[WgqrPension] [varchar](50) NULL,
	[WgqrPensionGUID] [uniqueidentifier] NULL,
	[DesignAlterAmountIsChanged] [tinyint] NULL,
	[ExcludingCbAmount] [money] NULL,
	[EstimatedAmount] [money] NULL,
	[InvalidCostAmount] [money] NULL,
	[IsYxys] [tinyint] NULL,
	[IsYcz] [tinyint] NULL,
	[IsJj] [tinyint] NULL,
	[IsGbsjfa] [tinyint] NULL,
	[IsYxgn] [tinyint] NULL,
	[IsCltz] [tinyint] NULL,
	[IsHb] [tinyint] NULL,
	[IsYyTc] [tinyint] NULL,
	[IsYxhtFt] [tinyint] NULL,
	[ImplementationTime] [datetime] NULL,
	[IsKzProgressPayment] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DesignAlter2Project
CREATE TABLE [dbo].[cb_DesignAlter2Project](
	[DesignAlter2Project] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[DesignAlterGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[DesignAlter2Project] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DesignAlter2Tax
CREATE TABLE [dbo].[cb_DesignAlter2Tax](
	[DesignAlter2TaxGUID] [uniqueidentifier] NOT NULL,
	[DesignAlterGuid] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxRate] [money] NULL,
	[ApplyAmount] [money] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[DesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxDesignAlterAmount] [money] NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NULL,
	[InputTaxDesignAlterAmount] [money] NULL,
	[InputTaxDesignAlterAmount_Bz] [money] NULL,
	[QrAmount] [money] NULL,
	[QrAmount_Bz] [money] NULL,
	[ExcludingTaxQrAmount] [money] NULL,
	[ExcludingTaxQrAmount_Bz] [money] NULL,
	[InputTaxQrAmount] [money] NULL,
	[InputTaxQrAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
 CONSTRAINT [PK_cb_DesignAlter2Tax] PRIMARY KEY CLUSTERED 
(
	[DesignAlter2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DesignAlterBudgetUse
CREATE TABLE [dbo].[cb_DesignAlterBudgetUse](
	[Contract2DesignGuid] [uniqueidentifier] NOT NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[CurrencyName] [varchar](50) NULL,
	[Rate] [decimal](14, 6) NULL,
	[ApplyAmount] [money] NOT NULL,
	[ApplyAmount_Bz] [money] NOT NULL,
	[DesignAlterAmount] [money] NOT NULL,
	[DesignAlterAmount_Bz] [money] NOT NULL,
	[QRAmount] [money] NOT NULL,
	[QRAmount_Bz] [money] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[YgAlterAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_Bz] [money] NOT NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[Contract2DesignGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectContract
CREATE TABLE [dbo].[cb_DtCostRecollectContract](
	[RecollectGUID] [uniqueidentifier] NOT NULL,
	[DtCostRecollectContractGUID] [uniqueidentifier] NOT NULL,
	[ContractCountMonth] [money] NOT NULL,
	[NoContractCountMonth] [money] NOT NULL,
	[ContractCountTotal] [money] NOT NULL,
	[NoContractCountTotal] [money] NOT NULL,
	[ContractAmountMonth] [money] NOT NULL,
	[NoContractAmountMonth] [money] NOT NULL,
	[ContractAmountTotal] [money] NOT NULL,
	[NoContractAmountTotal] [money] NOT NULL,
	[SiteCertCountMonth] [money] NOT NULL,
	[SiteCertCountTotal] [money] NOT NULL,
	[SiteCertAmountMonth] [money] NOT NULL,
	[SiteCertAmountTotal] [money] NOT NULL,
	[DesignAlterCountMonth] [money] NOT NULL,
	[DesignAlterCountTotal] [money] NOT NULL,
	[DesignAlterAmountMonth] [money] NOT NULL,
	[DesignAlterAmountTotal] [money] NOT NULL,
	[OtherAlterCountMonth] [money] NOT NULL,
	[OtherAlterCountTotal] [money] NOT NULL,
	[OtherAlterAmountMonth] [money] NOT NULL,
	[OtherAlterAmountTotal] [money] NOT NULL,
 CONSTRAINT [PK_cb_DtCostRecollectContract] PRIMARY KEY NONCLUSTERED 
(
	[DtCostRecollectContractGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectCost
CREATE TABLE [dbo].[cb_DtCostRecollectCost](
	[DtCostRecollectCostGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IfEndCost] [tinyint] NULL,
	[CostType] [varchar](20) NULL,
	[CostKind] [varchar](50) NULL,
	[CostCategory] [varchar](50) NULL,
	[IsBigCost] [tinyint] NULL,
	[ProjectCode] [varchar](100) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[Remarks] [varchar](max) NULL,
PRIMARY KEY CLUSTERED 
(
	[DtCostRecollectCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectDetails
CREATE TABLE [dbo].[cb_DtCostRecollectDetails](
	[RecollectDetailsGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostShortName] [varchar](400) NULL,
	[IsEndBudgetItem] [tinyint] NULL,
	[Type] [varchar](10) NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[DiffCostAlter] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[HtAmount_Bz_FX] [money] NULL,
	[SumAlterAmount_Bz_FX] [money] NULL,
	[YfsAmount] [money] NULL,
	[YGAlter] [money] NULL,
	[DfsBudget] [money] NULL,
	[LayoutSpare] [money] NULL,
	[Budget] [money] NULL,
	[Budget_Last] [money] NULL,
	[HtAmount_Bz_FX_Last] [money] NULL,
	[SumAlterAmount_Bz_FX_Last] [money] NULL,
	[YfsAmount_Last] [money] NULL,
	[YGAlter_Last] [money] NULL,
	[DtCost_Last] [money] NULL,
	[HtSate] [varchar](10) NULL,
	[DfsBudget_Last] [money] NULL,
	[ZTCost] [money] NULL,
	[ZTCost_Last] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectFX
CREATE TABLE [dbo].[cb_DtCostRecollectFX](
	[RecollectFxGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostShortName] [varchar](80) NULL,
	[IsEndBudgetItem] [tinyint] NULL,
	[TargetCost] [money] NULL,
	[AdjustCost] [money] NULL,
	[CurDtCost] [money] NULL,
	[DiffTargetCost] [money] NULL,
	[LastDtCost] [money] NULL,
	[DiffLastDtCost] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectProdBuildCost
CREATE TABLE [dbo].[cb_DtCostRecollectProdBuildCost](
	[ProductBuildCostGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[TargetBuildCost] [money] NULL,
	[DynamicBuildCost] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductBuildCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectProdProfit
CREATE TABLE [dbo].[cb_DtCostRecollectProdProfit](
	[ProductProfitGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[PlanSaleAmount] [money] NULL,
	[TargetCost] [money] NULL,
	[Target_SaleTaxExtraRate] [money] NULL,
	[Target_SaleTaxExtra] [money] NULL,
	[Target_LandIncrementTax] [money] NULL,
	[Target_BusinessTaxRate] [money] NULL,
	[Target_BusinessTax] [money] NULL,
	[PlanProfit] [money] NULL,
	[PlanCostProfitRate] [money] NULL,
	[PlanSaleProfitRate] [money] NULL,
	[FactSaleAmount] [money] NULL,
	[DynamicCost] [money] NULL,
	[Dynamic_SaleTaxExtraRate] [money] NULL,
	[Dynamic_SaleTaxExtra] [money] NULL,
	[Dynamic_LandIncrementTax] [money] NULL,
	[Dynamic_BusinessTaxRate] [money] NULL,
	[Dynamic_BusinessTax] [money] NULL,
	[FactProfit] [money] NULL,
	[FactCostProfitRate] [money] NULL,
	[FactSaleProfitRate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductProfitGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectProdSaleCost
CREATE TABLE [dbo].[cb_DtCostRecollectProdSaleCost](
	[ProductSaleCostGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[TargetSaleCost] [money] NULL,
	[DynamicSaleCost] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductSaleCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectProduct
CREATE TABLE [dbo].[cb_DtCostRecollectProduct](
	[DtCostRecollectProductGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ProductName] [varchar](40) NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[IsSale] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[DtCostRecollectProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtCostRecollectProject
CREATE TABLE [dbo].[cb_DtCostRecollectProject](
	[DtCostRecollectProjectGUID] [uniqueidentifier] NOT NULL,
	[RecollectGUID] [uniqueidentifier] NOT NULL,
	[ProjName] [varchar](400) NOT NULL,
	[BuildBeginDate] [datetime] NULL,
	[BuildEndDate] [datetime] NULL,
	[BuildArea] [money] NOT NULL,
	[SaleArea] [money] NULL,
	[OccupyArea] [money] NOT NULL,
	[Rjl] [money] NOT NULL,
 CONSTRAINT [PK_cb_DtCostRecollectProject] PRIMARY KEY NONCLUSTERED 
(
	[DtCostRecollectProjectGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_DtInvestPlan
CREATE TABLE [dbo].[cb_DtInvestPlan](
	[DtInvestPlanGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectCode] [varchar](100) NULL,
	[PlanMonth] [varchar](7) NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IfEndCost] [tinyint] NULL,
	[DtCost] [money] NULL,
	[UnassignedAmount] [money] NULL,
	[AssignedAmount] [money] NULL,
	[P1a] [money] NULL,
	[P1b] [money] NULL,
	[P2a] [money] NULL,
	[P2b] [money] NULL,
	[P3a] [money] NULL,
	[P3b] [money] NULL,
	[P4a] [money] NULL,
	[P4b] [money] NULL,
	[P5a] [money] NULL,
	[P5b] [money] NULL,
	[P6a] [money] NULL,
	[P6b] [money] NULL,
	[P7a] [money] NULL,
	[P7b] [money] NULL,
	[P8a] [money] NULL,
	[P8b] [money] NULL,
	[P9a] [money] NULL,
	[P9b] [money] NULL,
	[P10a] [money] NULL,
	[P10b] [money] NULL,
	[P11a] [money] NULL,
	[P11b] [money] NULL,
	[P12a] [money] NULL,
	[P12b] [money] NULL,
	[P13a] [money] NULL,
	[P13b] [money] NULL,
	[P14a] [money] NULL,
	[P14b] [money] NULL,
	[P15a] [money] NULL,
	[P15b] [money] NULL,
	[P16a] [money] NULL,
	[P16b] [money] NULL,
	[P17a] [money] NULL,
	[P17b] [money] NULL,
	[P18a] [money] NULL,
	[P18b] [money] NULL,
	[P19a] [money] NULL,
	[P19b] [money] NULL,
	[P20a] [money] NULL,
	[P20b] [money] NULL,
	[P21a] [money] NULL,
	[P21b] [money] NULL,
	[P22a] [money] NULL,
	[P22b] [money] NULL,
	[P23a] [money] NULL,
	[P23b] [money] NULL,
	[P24a] [money] NULL,
	[P24b] [money] NULL,
	[P25a] [money] NULL,
	[P25b] [money] NULL,
	[P26a] [money] NULL,
	[P26b] [money] NULL,
	[P27a] [money] NULL,
	[P27b] [money] NULL,
	[P28a] [money] NULL,
	[P28b] [money] NULL,
	[P29a] [money] NULL,
	[P29b] [money] NULL,
	[P30a] [money] NULL,
	[P30b] [money] NULL,
	[P31a] [money] NULL,
	[P31b] [money] NULL,
	[P32a] [money] NULL,
	[P32b] [money] NULL,
	[P33a] [money] NULL,
	[P33b] [money] NULL,
	[P34a] [money] NULL,
	[P34b] [money] NULL,
	[P35a] [money] NULL,
	[P35b] [money] NULL,
	[P36a] [money] NULL,
	[P36b] [money] NULL,
	[P37a] [money] NULL,
	[P37b] [money] NULL,
	[P38a] [money] NULL,
	[P38b] [money] NULL,
	[P39a] [money] NULL,
	[P39b] [money] NULL,
	[P40a] [money] NULL,
	[P40b] [money] NULL,
	[P41a] [money] NULL,
	[P41b] [money] NULL,
	[P42a] [money] NULL,
	[P42b] [money] NULL,
	[P43a] [money] NULL,
	[P43b] [money] NULL,
	[P44a] [money] NULL,
	[P44b] [money] NULL,
	[P45a] [money] NULL,
	[P45b] [money] NULL,
	[P46a] [money] NULL,
	[P46b] [money] NULL,
	[P47a] [money] NULL,
	[P47b] [money] NULL,
	[P48a] [money] NULL,
	[P48b] [money] NULL,
	[P49a] [money] NULL,
	[P49b] [money] NULL,
	[P50a] [money] NULL,
	[P50b] [money] NULL,
	[P51a] [money] NULL,
	[P51b] [money] NULL,
	[P52a] [money] NULL,
	[P52b] [money] NULL,
	[P53a] [money] NULL,
	[P53b] [money] NULL,
	[P54a] [money] NULL,
	[P54b] [money] NULL,
	[P55a] [money] NULL,
	[P55b] [money] NULL,
	[P56a] [money] NULL,
	[P56b] [money] NULL,
	[P57a] [money] NULL,
	[P57b] [money] NULL,
	[P58a] [money] NULL,
	[P58b] [money] NULL,
	[P59a] [money] NULL,
	[P59b] [money] NULL,
	[P60a] [money] NULL,
	[P60b] [money] NULL,
	[P61a] [money] NULL,
	[P61b] [money] NULL,
	[P62a] [money] NULL,
	[P62b] [money] NULL,
	[P63a] [money] NULL,
	[P63b] [money] NULL,
	[P64a] [money] NULL,
	[P64b] [money] NULL,
	[P65a] [money] NULL,
	[P65b] [money] NULL,
	[P66a] [money] NULL,
	[P66b] [money] NULL,
	[P67a] [money] NULL,
	[P67b] [money] NULL,
	[P68a] [money] NULL,
	[P68b] [money] NULL,
	[P69a] [money] NULL,
	[P69b] [money] NULL,
	[P70a] [money] NULL,
	[P70b] [money] NULL,
	[P71a] [money] NULL,
	[P71b] [money] NULL,
	[P72a] [money] NULL,
	[P72b] [money] NULL,
	[P73a] [money] NULL,
	[P73b] [money] NULL,
	[P74a] [money] NULL,
	[P74b] [money] NULL,
	[P75a] [money] NULL,
	[P75b] [money] NULL,
	[P76a] [money] NULL,
	[P76b] [money] NULL,
	[P77a] [money] NULL,
	[P77b] [money] NULL,
	[P78a] [money] NULL,
	[P78b] [money] NULL,
	[P79a] [money] NULL,
	[P79b] [money] NULL,
	[P80a] [money] NULL,
	[P80b] [money] NULL,
	[P81a] [money] NULL,
	[P81b] [money] NULL,
	[P82a] [money] NULL,
	[P82b] [money] NULL,
	[P83a] [money] NULL,
	[P83b] [money] NULL,
	[P84a] [money] NULL,
	[P84b] [money] NULL,
	[P85a] [money] NULL,
	[P85b] [money] NULL,
	[P86a] [money] NULL,
	[P86b] [money] NULL,
	[P87a] [money] NULL,
	[P87b] [money] NULL,
	[P88a] [money] NULL,
	[P88b] [money] NULL,
	[P89a] [money] NULL,
	[P89b] [money] NULL,
	[P90a] [money] NULL,
	[P90b] [money] NULL,
	[P91a] [money] NULL,
	[P91b] [money] NULL,
	[P92a] [money] NULL,
	[P92b] [money] NULL,
	[P93a] [money] NULL,
	[P93b] [money] NULL,
	[P94a] [money] NULL,
	[P94b] [money] NULL,
	[P95a] [money] NULL,
	[P95b] [money] NULL,
	[P96a] [money] NULL,
	[P96b] [money] NULL,
	[P97a] [money] NULL,
	[P97b] [money] NULL,
	[P98a] [money] NULL,
	[P98b] [money] NULL,
	[P99a] [money] NULL,
	[P99b] [money] NULL,
	[P100a] [money] NULL,
	[P100b] [money] NULL,
	[P101a] [money] NULL,
	[P101b] [money] NULL,
	[P102a] [money] NULL,
	[P102b] [money] NULL,
	[P103a] [money] NULL,
	[P103b] [money] NULL,
	[P104a] [money] NULL,
	[P104b] [money] NULL,
	[P105a] [money] NULL,
	[P105b] [money] NULL,
	[P106a] [money] NULL,
	[P106b] [money] NULL,
	[P107a] [money] NULL,
	[P107b] [money] NULL,
	[P108a] [money] NULL,
	[P108b] [money] NULL,
	[P109a] [money] NULL,
	[P109b] [money] NULL,
	[P110a] [money] NULL,
	[P110b] [money] NULL,
	[P111a] [money] NULL,
	[P111b] [money] NULL,
	[P112a] [money] NULL,
	[P112b] [money] NULL,
	[P113a] [money] NULL,
	[P113b] [money] NULL,
	[P114a] [money] NULL,
	[P114b] [money] NULL,
	[P115a] [money] NULL,
	[P115b] [money] NULL,
	[P116a] [money] NULL,
	[P116b] [money] NULL,
	[P117a] [money] NULL,
	[P117b] [money] NULL,
	[P118a] [money] NULL,
	[P118b] [money] NULL,
	[P119a] [money] NULL,
	[P119b] [money] NULL,
	[P120a] [money] NULL,
	[P120b] [money] NULL,
 CONSTRAINT [PK_cb_DtInvestPlan] PRIMARY KEY CLUSTERED 
(
	[DtInvestPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_EndCost2Cost
CREATE TABLE [dbo].[cb_EndCost2Cost](
	[ProjectCode] [varchar](100) NOT NULL,
	[EndCostCode] [varchar](100) NOT NULL,
	[Costcode] [varchar](100) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ExecutingBudgetMappingTmp
CREATE TABLE [dbo].[cb_ExecutingBudgetMappingTmp](
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[ExecutingBudgetGUIDUsed] [uniqueidentifier] NULL,
	[ExecutingBudgetGUIDNotUsed] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Expense
CREATE TABLE [dbo].[cb_Expense](
	[ExpenseGUID] [uniqueidentifier] NULL,
	[Subject] [text] NULL,
	[ExpenseCode] [varchar](50) NULL,
	[AppliedByName] [varchar](50) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[ApplyDate] [datetime] NULL,
	[ExpenseTypeGUID] [uniqueidentifier] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ExpenseAmount_Bz] [money] NULL,
	[ExpenseAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[PayAmount_Bz] [money] NULL,
	[PayAmount] [money] NULL,
	[Remarks] [text] NULL,
	[PayState] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ApplyState] [varchar](50) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[ExpenseTypeName] [varchar](100) NULL,
	[ApproveBy] [uniqueidentifier] NULL,
	[FinishDateTime] [datetime] NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[Rate] [money] NULL,
	[PayMode] [varchar](30) NULL,
	[ReceiverName] [varchar](20) NULL,
	[ReceiverGUID] [uniqueidentifier] NULL,
	[ReceiverBankName] [varchar](100) NULL,
	[ReceiverBankAccounts] [varchar](50) NULL,
	[ReceiveType] [varchar](10) NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[IsDeductibleloan] [bit] NULL,
	[IsCompanyExpense] [bit] NULL,
	[TotalInvoiceCount] [int] NULL,
	[TaxableInvoiceCount] [int] NULL,
	[IsCopy] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ExpenseDtl
CREATE TABLE [dbo].[cb_ExpenseDtl](
	[ExpenseDtlGUID] [uniqueidentifier] NULL,
	[ExpenseGUID] [uniqueidentifier] NULL,
	[ExpenseDtlName] [varchar](100) NULL,
	[Brief] [text] NULL,
	[OccurDate] [datetime] NULL,
	[Amount_Bz] [money] NULL,
	[IsCanDeductible] [int] NULL,
	[InputTaxAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[AccountingSubjectGUID] [uniqueidentifier] NULL,
	[AccountingSubject] [varchar](200) NULL,
	[InvoiceXml] [nvarchar](max) NULL,
	[IsCopy] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_FKPlanCFRule
CREATE TABLE [dbo].[cb_FKPlanCFRule](
	[FKPlanCfRuleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[ProjectCode] [varchar](100) NULL,
	[Scale] [money] NULL,
	[ScaleDtl] [decimal](27, 13) NULL,
	[ExcludingTaxScale] [money] NOT NULL,
	[ExcludingTaxScaleDtl] [decimal](27, 13) NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[FKPlanCfRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_FKSPType
CREATE TABLE [dbo].[cb_FKSPType](
	[FKSPTypeGUID] [uniqueidentifier] NOT NULL,
	[FKSPTypeCode] [varchar](50) NULL,
	[FKSPTypeName] [varchar](50) NULL,
	[FKSPClass] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProcessGUID] [uniqueidentifier] NULL,
	[Remarks] [text] NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_FKSPType] PRIMARY KEY CLUSTERED 
(
	[FKSPTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_FKSPTypeStationRights
CREATE TABLE [dbo].[cb_FKSPTypeStationRights](
	[FKSPTypeStationRightsGUID] [uniqueidentifier] NOT NULL,
	[StationGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[FKSPTypeGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_FKSPTypeStationRights] PRIMARY KEY NONCLUSTERED 
(
	[FKSPTypeStationRightsGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_FKSPType_History
CREATE TABLE [dbo].[cb_FKSPType_History](
	[FKSPTypeGUID] [uniqueidentifier] NOT NULL,
	[FKSPTypeCode] [varchar](50) NULL,
	[FKSPTypeName] [varchar](50) NULL,
	[FKSPClass] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProcessGUID] [uniqueidentifier] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_FKSPType_History] PRIMARY KEY CLUSTERED 
(
	[FKSPTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Fee
CREATE TABLE [dbo].[cb_Fee](
	[FeeGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[PayFlag] [varchar](2) NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[YfDate] [datetime] NULL,
	[YfAmount] [money] NULL,
	[Ye] [money] NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[Remarks] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[YfAmount_Bz] [money] NULL,
 CONSTRAINT [PK_cb_Fee] PRIMARY KEY CLUSTERED 
(
	[FeeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_FtBillRef
CREATE TABLE [dbo].[cb_FtBillRef](
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[FtAmount] [money] NULL,
	[YgAmount] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_FtTargetRef
CREATE TABLE [dbo].[cb_FtTargetRef](
	[ProjGUID] [uniqueidentifier] NULL,
	[FtTargetGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[TargetCost] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_FwHtModule
CREATE TABLE [dbo].[cb_FwHtModule](
	[FwHtModuleGUID] [uniqueidentifier] NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[FwHtModuleCode] [varchar](300) NULL,
	[FwHtModuleID] [varchar](300) NULL,
	[FwHtModuleName] [varchar](300) NULL,
	[FwHtModuleDesc] [varchar](2000) NULL,
PRIMARY KEY CLUSTERED 
(
	[FwHtModuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_FwHtModuleMiddle
CREATE TABLE [dbo].[cb_FwHtModuleMiddle](
	[FwHtModuleMiddleGUID] [uniqueidentifier] NOT NULL,
	[FwHtModuleCode] [varchar](300) NULL,
	[FwHtModuleID] [varchar](300) NULL,
	[FwHtModuleName] [varchar](300) NULL,
	[FwHtModuleDesc] [varchar](2000) NULL,
	[CreateTime] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[FwHtModuleMiddleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_GclBill
CREATE TABLE [dbo].[cb_GclBill](
	[GclBillGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[GclBillName] [varchar](100) NULL,
	[Unit] [varchar](16) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [money] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_GclBill] PRIMARY KEY NONCLUSTERED 
(
	[GclBillGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_GclBillJh
CREATE TABLE [dbo].[cb_GclBillJh](
	[GclBillJhGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[GclBillName] [varchar](100) NULL,
	[Unit] [varchar](16) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [money] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_GclBillJh] PRIMARY KEY CLUSTERED 
(
	[GclBillJhGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_GclTemplet
CREATE TABLE [dbo].[cb_GclTemplet](
	[GclTempletGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[GclBillName] [varchar](100) NULL,
	[GclTypeCode] [varchar](100) NULL,
	[Unit] [varchar](16) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [money] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_GclTemplet] PRIMARY KEY CLUSTERED 
(
	[GclTempletGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_GclType
CREATE TABLE [dbo].[cb_GclType](
	[GclTypeGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[GclTypeShortCode] [varchar](10) NULL,
	[GclTypeCode] [varchar](100) NULL,
	[GclTypeShortName] [varchar](40) NULL,
	[GclTypeName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_GclType] PRIMARY KEY CLUSTERED 
(
	[GclTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter
CREATE TABLE [dbo].[cb_HTAlter](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL,
	[JrWxCbYgAmount] [money] NULL,
	[IsYbGc] [int] NOT NULL,
	[AlterOrderCode] [varchar](800) NULL,
	[InvalidCostAmount] [money] NULL,
	[WgJzYsFinishTime] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter20200326bak
CREATE TABLE [dbo].[cb_HTAlter20200326bak](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter20200407
CREATE TABLE [dbo].[cb_HTAlter20200407](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter2021121701
CREATE TABLE [dbo].[cb_HTAlter2021121701](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter20220303
CREATE TABLE [dbo].[cb_HTAlter20220303](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter20221031
CREATE TABLE [dbo].[cb_HTAlter20221031](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL,
	[JrWxCbYgAmount] [money] NULL,
	[IsYbGc] [int] NOT NULL,
	[AlterOrderCode] [varchar](800) NULL,
	[InvalidCostAmount] [money] NULL,
	[WgJzYsFinishTime] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTAlter2Tax
CREATE TABLE [dbo].[cb_HTAlter2Tax](
	[HTAlter2TaxGUID] [uniqueidentifier] NOT NULL,
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[IsQR] [tinyint] NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxRate] [money] NULL,
	[ApplyAmount] [money] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[AlterAmount] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[QrApplyAmount] [money] NULL,
	[QrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[QrAlterAmount] [money] NULL,
	[QrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_HTAlter2Tax] PRIMARY KEY CLUSTERED 
(
	[HTAlter2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HTAlter_zyq2021_08_05
CREATE TABLE [dbo].[cb_HTAlter_zyq2021_08_05](
	[HTAlterGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterClass] [varchar](16) NULL,
	[AlterName] [varchar](300) NULL,
	[AlterType] [varchar](100) NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[PresentDeptCode] [varchar](500) NULL,
	[AlterReason] [varchar](30) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[Budgeteer] [varchar](20) NULL,
	[AlterContent] [text] NULL,
	[PaymentRemarks] [text] NULL,
	[AlterDate] [datetime] NULL,
	[AlterAmount] [money] NULL,
	[Remarks] [text] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveState] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ApproveLog] [text] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[AlterAmount_Bz] [money] NULL,
	[AlterCodeFormat] [varchar](800) NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptUseInfo] [varchar](8000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptName] [varchar](50) NULL,
	[IsQR] [tinyint] NULL,
	[QRAmount] [money] NULL,
	[QRAmount_bz] [money] NULL,
	[IsSGCR] [tinyint] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Qrr] [varchar](30) NULL,
	[QrDate] [datetime] NULL,
	[QrrGuid] [uniqueidentifier] NULL,
	[DesignAlterGuid] [uniqueidentifier] NULL,
	[QrApplyAmount] [money] NOT NULL,
	[QrApplyAmount_Bz] [money] NOT NULL,
	[QrRemarks] [varchar](2000) NULL,
	[QrApproveState] [varchar](30) NULL,
	[InvaildStatus] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildReason] [varchar](1000) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[ReportState] [varchar](20) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[ReportDate] [datetime] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[DesignAlterName] [varchar](400) NULL,
	[QrProcessGuid] [uniqueidentifier] NOT NULL,
	[QrAlterAmount_Bz] [money] NOT NULL,
	[QrAlterAmount] [money] NOT NULL,
	[TjDate] [datetime] NULL,
	[QrTjDate] [datetime] NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxAlterAmount] [money] NULL,
	[InputTaxAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxAlterAmount] [money] NULL,
	[ExcludingTaxAlterAmount_Bz] [money] NULL,
	[InputTaxQrApplyAmount] [money] NULL,
	[InputTaxQrApplyAmount_Bz] [money] NULL,
	[InputTaxQrAlterAmount] [money] NULL,
	[InputTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxQrApplyAmount] [money] NULL,
	[ExcludingTaxQrApplyAmount_Bz] [money] NULL,
	[ExcludingTaxQrAlterAmount] [money] NULL,
	[ExcludingTaxQrAlterAmount_Bz] [money] NULL,
	[ExcludingTaxYcfAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount] [money] NOT NULL,
	[ExcludingTaxQRAmount_bz] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[AlterAmountIsChanged] [tinyint] NULL,
	[QrAlterAmountIsChanged] [tinyint] NULL,
	[QrApproveStateNew] [varchar](30) NULL,
	[QrrNew] [varchar](30) NULL,
	[QrRemarksNew] [varchar](2000) NULL,
	[QrrGuidNew] [uniqueidentifier] NULL,
	[QrDateNew] [datetime] NULL,
	[QrProcessGuidNew] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTBalance
CREATE TABLE [dbo].[cb_HTBalance](
	[HTBalanceGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BalanceType] [varchar](10) NULL,
	[BalanceDocNo] [varchar](100) NULL,
	[TotalAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[BalanceAmount] [money] NULL,
	[BalanceDate] [datetime] NULL,
	[BxPercent] [money] NULL,
	[BxAmount] [money] NULL,
	[BxDate] [datetime] NULL,
	[SumScheduleAmount] [money] NULL,
	[RemainScheduleAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[SumAlterAmount] [money] NULL,
	[AdjustAmount] [money] NULL,
	[Remarks] [text] NULL,
	[CreatedOn] [datetime] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[BalanceAmount_Bz] [money] NULL,
	[ItemAmount_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[AdjustAmount1] [money] NULL,
	[ApproveState] [varchar](20) NULL,
	[DeptUseInfo] [varchar](1000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbDeptName] [varchar](50) NULL,
	[InputTaxBalanceAmount] [money] NULL,
	[InputTaxBalanceAmount_Bz] [money] NULL,
	[ExcludingTaxBalanceAmount] [money] NULL,
	[ExcludingTaxBalanceAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[ExcludingTaxHtAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount1] [money] NOT NULL,
	[ExcludingTaxItemAmount] [money] NOT NULL,
	[ExcludingTaxOtherDeduct] [money] NOT NULL,
	[ExcludingTaxBxAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[YiShenRiQi] [datetime] NULL,
	[ErShenRiQi] [datetime] NULL,
	[SanShenRiQi] [datetime] NULL,
 CONSTRAINT [PK_cb_HTBalance] PRIMARY KEY NONCLUSTERED 
(
	[HTBalanceGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTBalance0119
CREATE TABLE [dbo].[cb_HTBalance0119](
	[HTBalanceGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BalanceType] [varchar](10) NULL,
	[BalanceDocNo] [varchar](100) NULL,
	[TotalAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[BalanceAmount] [money] NULL,
	[BalanceDate] [datetime] NULL,
	[BxPercent] [money] NULL,
	[BxAmount] [money] NULL,
	[BxDate] [datetime] NULL,
	[SumScheduleAmount] [money] NULL,
	[RemainScheduleAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[SumAlterAmount] [money] NULL,
	[AdjustAmount] [money] NULL,
	[Remarks] [text] NULL,
	[CreatedOn] [datetime] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[BalanceAmount_Bz] [money] NULL,
	[ItemAmount_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[AdjustAmount1] [money] NULL,
	[ApproveState] [varchar](20) NULL,
	[DeptUseInfo] [varchar](1000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbDeptName] [varchar](50) NULL,
	[InputTaxBalanceAmount] [money] NULL,
	[InputTaxBalanceAmount_Bz] [money] NULL,
	[ExcludingTaxBalanceAmount] [money] NULL,
	[ExcludingTaxBalanceAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[ExcludingTaxHtAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount1] [money] NOT NULL,
	[ExcludingTaxItemAmount] [money] NOT NULL,
	[ExcludingTaxOtherDeduct] [money] NOT NULL,
	[ExcludingTaxBxAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[YiShenRiQi] [datetime] NULL,
	[ErShenRiQi] [datetime] NULL,
	[SanShenRiQi] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTBalance20200327
CREATE TABLE [dbo].[cb_HTBalance20200327](
	[HTBalanceGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BalanceType] [varchar](10) NULL,
	[BalanceDocNo] [varchar](100) NULL,
	[TotalAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[BalanceAmount] [money] NULL,
	[BalanceDate] [datetime] NULL,
	[BxPercent] [money] NULL,
	[BxAmount] [money] NULL,
	[BxDate] [datetime] NULL,
	[SumScheduleAmount] [money] NULL,
	[RemainScheduleAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[SumAlterAmount] [money] NULL,
	[AdjustAmount] [money] NULL,
	[Remarks] [text] NULL,
	[CreatedOn] [datetime] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[BalanceAmount_Bz] [money] NULL,
	[ItemAmount_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[AdjustAmount1] [money] NULL,
	[ApproveState] [varchar](20) NULL,
	[DeptUseInfo] [varchar](1000) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](50) NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbDeptName] [varchar](50) NULL,
	[InputTaxBalanceAmount] [money] NULL,
	[InputTaxBalanceAmount_Bz] [money] NULL,
	[ExcludingTaxBalanceAmount] [money] NULL,
	[ExcludingTaxBalanceAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[ExcludingTaxHtAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount1] [money] NOT NULL,
	[ExcludingTaxItemAmount] [money] NOT NULL,
	[ExcludingTaxOtherDeduct] [money] NOT NULL,
	[ExcludingTaxBxAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTBalance2Tax
CREATE TABLE [dbo].[cb_HTBalance2Tax](
	[HTBalance2TaxGUID] [uniqueidentifier] NOT NULL,
	[HTBalanceGUID] [uniqueidentifier] NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxRate] [money] NULL,
	[BalanceAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[ExcludingTaxBalanceAmount] [money] NULL,
	[ExcludingTaxBalanceAmount_Bz] [money] NULL,
	[InputTaxBalanceAmount] [money] NULL,
	[InputTaxBalanceAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_HTBalance2Tax] PRIMARY KEY CLUSTERED 
(
	[HTBalance2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HTBorrow
CREATE TABLE [dbo].[cb_HTBorrow](
	[HTBorrowGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BorrowDate] [datetime] NULL,
	[BorrowedBy] [varchar](20) NULL,
	[ReturnDateJh] [datetime] NULL,
	[ReturnDateSj] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_HTBorrow] PRIMARY KEY NONCLUSTERED 
(
	[HTBorrowGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTDeduct
CREATE TABLE [dbo].[cb_HTDeduct](
	[HTDeductGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[RecordType] [varchar](10) NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[DeductType] [varchar](10) NULL,
	[HTDeductName] [varchar](40) NULL,
	[DeductAmount] [money] NULL,
	[IfBalanceUsed] [tinyint] NULL,
 CONSTRAINT [PK_cb_HTDeduct] PRIMARY KEY NONCLUSTERED 
(
	[HTDeductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply
CREATE TABLE [dbo].[cb_HTFKApply](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply20220610
CREATE TABLE [dbo].[cb_HTFKApply20220610](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply20231025
CREATE TABLE [dbo].[cb_HTFKApply20231025](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply2024072201
CREATE TABLE [dbo].[cb_HTFKApply2024072201](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply20240816
CREATE TABLE [dbo].[cb_HTFKApply20240816](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply20241113
CREATE TABLE [dbo].[cb_HTFKApply20241113](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply20250414
CREATE TABLE [dbo].[cb_HTFKApply20250414](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKApply2Tax
CREATE TABLE [dbo].[cb_HTFKApply2Tax](
	[HTFKApply2TaxGUID] [uniqueidentifier] NOT NULL,
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[ApplyAmount] [money] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[TaxRate] [money] NULL,
	[ExcludingApplyAmount] [money] NULL,
	[ExcludingApplyAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[InputApplyAmount_Bz] [money] NULL,
	[Remark] [varchar](100) NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_HTFKApply2Tax] PRIMARY KEY CLUSTERED 
(
	[HTFKApply2TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HTFKApplyCfDetail
CREATE TABLE [dbo].[cb_HTFKApplyCfDetail](
	[DetailGUID] [uniqueidentifier] NOT NULL,
	[HTFKApplyGUID] [uniqueidentifier] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[DetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HTFKCondition
CREATE TABLE [dbo].[cb_HTFKCondition](
	[HTFKConditionGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FkDate] [datetime] NULL,
	[FKRate] [money] NULL,
	[FKAmount] [money] NULL,
	[FKCondition] [text] NULL,
	[FKType] [varchar](30) NULL,
	[FKName] [varchar](30) NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan
CREATE TABLE [dbo].[cb_HTFKPlan](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan20231025
CREATE TABLE [dbo].[cb_HTFKPlan20231025](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan20240206
CREATE TABLE [dbo].[cb_HTFKPlan20240206](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan20240718
CREATE TABLE [dbo].[cb_HTFKPlan20240718](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan20250107
CREATE TABLE [dbo].[cb_HTFKPlan20250107](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan230524
CREATE TABLE [dbo].[cb_HTFKPlan230524](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan23052401
CREATE TABLE [dbo].[cb_HTFKPlan23052401](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKPlan_History
CREATE TABLE [dbo].[cb_HTFKPlan_History](
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[JhfkDate] [datetime] NULL,
	[JhfkAmount] [money] NULL,
	[JhfkRemarks] [varchar](max) NULL,
	[ApproveState] [varchar](10) NULL,
	[RecordType] [tinyint] NULL,
	[MonthPlanYe] [money] NULL,
	[JhState] [varchar](10) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[JhfkAmount_Bz] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DeptCode] [varchar](500) NULL,
	[NoPayPlanAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsRepayDebt] [tinyint] NULL,
	[RepayDebtGUID] [uniqueidentifier] NULL,
	[RepayDebtFtGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ExecuteUserGUID] [uniqueidentifier] NULL,
	[OperTag] [varchar](10) NULL,
	[OperDate] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTFKapply20240722
CREATE TABLE [dbo].[cb_HTFKapply20240722](
	[HTFKApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[ApplyCode] [varchar](800) NULL,
	[ApplyState] [varchar](16) NULL,
	[PayState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[ApplyDate] [datetime] NULL,
	[IsNeedBalance] [tinyint] NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ApplyAmount] [money] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[DfdkAmount_Bz] [money] NULL,
	[DfdkAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[YfAmount_Bz] [money] NULL,
	[YfAmount] [money] NULL,
	[RemainAmount] [money] NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[CfAmount] [varchar](50) NULL,
	[CfState] [varchar](50) NULL,
	[RecordType] [tinyint] NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[ApplyRemarks] [text] NULL,
	[ApplyClass] [tinyint] NULL,
	[ApplyTypeGUID] [uniqueidentifier] NULL,
	[ApplyTypeName] [varchar](100) NULL,
	[BudgetInfo] [varchar](max) NULL,
	[BudgetColor] [varchar](10) NULL,
	[StockInfo] [nvarchar](1000) NULL,
	[ApplyRateInfo] [nvarchar](1000) NULL,
	[ApplyRateColor] [varchar](10) NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumApplyAmount] [money] NULL,
	[ScheduleConsultAmount] [money] NULL,
	[MonthPlanYe] [money] NULL,
	[MonthContractPlanAmount] [money] NULL,
	[MonthContractApplyAmount] [money] NULL,
	[MonthContractRemainAmount] [money] NULL,
	[MonthPersonPlanAmount] [money] NULL,
	[MonthPersonApplyAmount] [money] NULL,
	[MonthPersonRemainAmount] [money] NULL,
	[MonthDeptPlanAmount] [money] NULL,
	[MonthDeptApplyAmount] [money] NULL,
	[MonthDeptRemainAmount] [money] NULL,
	[ApplyType] [varchar](10) NULL,
	[ApplyCodeFormat] [varchar](800) NULL,
	[IsSpecialFlow] [tinyint] NULL,
	[MonthContractSPAmount] [money] NULL,
	[MonthPersonSpAmount] [money] NULL,
	[MonthDeptSpAmount] [money] NULL,
	[ZjPlanControl] [varchar](20) NULL,
	[OriginalApplyAmount_Bz] [money] NULL,
	[OriginalApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NOT NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NOT NULL,
	[InvoiceAmount] [money] NULL,
	[SumInvoiceAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[ZbjApplyAmount] [money] NULL,
	[ZbjCutPayAmount] [money] NULL,
	[IsOutput] [tinyint] NULL,
	[TranceTime] [datetime] NULL,
	[TrancedByGUID] [uniqueidentifier] NULL,
	[TranceErrorMessage] [varchar](8000) NULL,
	[IsDzfPay] [tinyint] NULL,
	[IsDcPay] [tinyint] NULL,
	[BcfkkAmount] [money] NULL,
	[BckjsdAmount] [money] NULL,
	[BcfkkAmount_Bz] [money] NULL,
	[BckjsdAmount_Bz] [money] NULL,
	[ProviderBankGUID] [uniqueidentifier] NULL,
	[ApplyOrder] [int] NULL,
	[CDCfAmount] [money] NULL,
	[DHCfAmount] [money] NULL,
	[CDCfAmount_Bz] [money] NULL,
	[DHCfAmount_Bz] [money] NULL,
	[InputApplyAmount] [money] NULL,
	[IsBl] [tinyint] NULL,
	[SPFTAmount] [money] NULL,
	[BLFTAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTForecast
CREATE TABLE [dbo].[cb_HTForecast](
	[HTForecastGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[EmendDate] [datetime] NULL,
	[EmendedBy] [varchar](20) NULL,
	[LastAmount] [money] NULL,
	[CurrentAmount] [money] NULL,
	[EmendReason] [text] NULL,
 CONSTRAINT [PK_cb_HTForecast] PRIMARY KEY CLUSTERED 
(
	[HTForecastGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0116
CREATE TABLE [dbo].[cb_HTSchedule0116](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0204
CREATE TABLE [dbo].[cb_HTSchedule0204](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0226
CREATE TABLE [dbo].[cb_HTSchedule0226](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0327
CREATE TABLE [dbo].[cb_HTSchedule0327](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0401
CREATE TABLE [dbo].[cb_HTSchedule0401](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule04011
CREATE TABLE [dbo].[cb_HTSchedule04011](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0717
CREATE TABLE [dbo].[cb_HTSchedule0717](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule07171
CREATE TABLE [dbo].[cb_HTSchedule07171](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0801
CREATE TABLE [dbo].[cb_HTSchedule0801](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule0829
CREATE TABLE [dbo].[cb_HTSchedule0829](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1016
CREATE TABLE [dbo].[cb_HTSchedule1016](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1118
CREATE TABLE [dbo].[cb_HTSchedule1118](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1120
CREATE TABLE [dbo].[cb_HTSchedule1120](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule11201
CREATE TABLE [dbo].[cb_HTSchedule11201](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1121
CREATE TABLE [dbo].[cb_HTSchedule1121](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1125
CREATE TABLE [dbo].[cb_HTSchedule1125](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule12
CREATE TABLE [dbo].[cb_HTSchedule12](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1205
CREATE TABLE [dbo].[cb_HTSchedule1205](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1217
CREATE TABLE [dbo].[cb_HTSchedule1217](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1222
CREATE TABLE [dbo].[cb_HTSchedule1222](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1223
CREATE TABLE [dbo].[cb_HTSchedule1223](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1224
CREATE TABLE [dbo].[cb_HTSchedule1224](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1227
CREATE TABLE [dbo].[cb_HTSchedule1227](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule1229
CREATE TABLE [dbo].[cb_HTSchedule1229](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240524
CREATE TABLE [dbo].[cb_HTSchedule20240524](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240717
CREATE TABLE [dbo].[cb_HTSchedule20240717](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240820
CREATE TABLE [dbo].[cb_HTSchedule20240820](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule202408201
CREATE TABLE [dbo].[cb_HTSchedule202408201](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240821
CREATE TABLE [dbo].[cb_HTSchedule20240821](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240822
CREATE TABLE [dbo].[cb_HTSchedule20240822](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240909
CREATE TABLE [dbo].[cb_HTSchedule20240909](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240911
CREATE TABLE [dbo].[cb_HTSchedule20240911](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20240923
CREATE TABLE [dbo].[cb_HTSchedule20240923](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241104
CREATE TABLE [dbo].[cb_HTSchedule20241104](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241112
CREATE TABLE [dbo].[cb_HTSchedule20241112](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241115
CREATE TABLE [dbo].[cb_HTSchedule20241115](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241120
CREATE TABLE [dbo].[cb_HTSchedule20241120](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule2024120901
CREATE TABLE [dbo].[cb_HTSchedule2024120901](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241211
CREATE TABLE [dbo].[cb_HTSchedule20241211](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241219
CREATE TABLE [dbo].[cb_HTSchedule20241219](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241224
CREATE TABLE [dbo].[cb_HTSchedule20241224](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20241230
CREATE TABLE [dbo].[cb_HTSchedule20241230](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250120
CREATE TABLE [dbo].[cb_HTSchedule20250120](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250121
CREATE TABLE [dbo].[cb_HTSchedule20250121](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250124
CREATE TABLE [dbo].[cb_HTSchedule20250124](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250126
CREATE TABLE [dbo].[cb_HTSchedule20250126](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule202501261
CREATE TABLE [dbo].[cb_HTSchedule202501261](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250512
CREATE TABLE [dbo].[cb_HTSchedule20250512](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250514
CREATE TABLE [dbo].[cb_HTSchedule20250514](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250516
CREATE TABLE [dbo].[cb_HTSchedule20250516](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250606
CREATE TABLE [dbo].[cb_HTSchedule20250606](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250616
CREATE TABLE [dbo].[cb_HTSchedule20250616](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250628
CREATE TABLE [dbo].[cb_HTSchedule20250628](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250714
CREATE TABLE [dbo].[cb_HTSchedule20250714](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250722
CREATE TABLE [dbo].[cb_HTSchedule20250722](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250812
CREATE TABLE [dbo].[cb_HTSchedule20250812](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule20250820
CREATE TABLE [dbo].[cb_HTSchedule20250820](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HTSchedule311
CREATE TABLE [dbo].[cb_HTSchedule311](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HkbProductCompare
CREATE TABLE [dbo].[cb_HkbProductCompare](
	[HkbProductCompareGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[ProductName] [varchar](40) NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[IsSale] [tinyint] NULL,
	[IsCYFT] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[Remarks] [text] NULL,
	[OccupyArea] [money] NULL,
	[OccupyRate] [money] NULL,
	[SaleArea] [money] NULL,
	[SaleRate] [money] NULL,
	[BuildArea] [money] NULL,
	[BuildRate] [money] NULL,
	[InnerArea] [money] NULL,
	[InnerRate] [money] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[PreNoSaleArea] [money] NULL,
	[Mode] [tinyint] NULL,
	[GeneralHouseRate] [money] NULL,
	[NonGeneralHouseRate] [money] NULL,
	[GeneralHouseArea] [money] NULL,
	[NonGeneralHouseArea] [money] NULL,
	[CwPrice] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
	[GeneralHouseCoverArea] [money] NULL,
	[NoGeneralHouseCoverArea] [money] NULL,
	[OtherArea] [money] NULL,
	[OtherRate] [money] NULL,
	[OtherCoverArea] [money] NULL,
	[GeneralHouseCoverAreaRate] [money] NULL,
	[NoGeneralHouseCoverAreaRate] [money] NULL,
	[OtherCoverAreaRate] [money] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTCOMPARE] PRIMARY KEY CLUSTERED 
(
	[HkbProductCompareGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HkbProductHistory
CREATE TABLE [dbo].[cb_HkbProductHistory](
	[HkbProductHistoryGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[ProductName] [varchar](40) NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[IsSale] [tinyint] NULL,
	[IsCYFT] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[Remarks] [text] NULL,
	[OccupyArea] [money] NULL,
	[OccupyRate] [money] NULL,
	[SaleArea] [money] NULL,
	[SaleRate] [money] NULL,
	[BuildArea] [money] NULL,
	[BuildRate] [money] NULL,
	[InnerArea] [money] NULL,
	[InnerRate] [money] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[PreNoSaleArea] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTHISTORY] PRIMARY KEY CLUSTERED 
(
	[HkbProductHistoryGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HkbProductIndexCompare
CREATE TABLE [dbo].[cb_HkbProductIndexCompare](
	[HkbProductIndexCompareGUID] [uniqueidentifier] NOT NULL,
	[ProductIndexGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[BuildTerraArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[BuildNum] [money] NULL,
	[FloorNum] [money] NULL,
	[CellNum] [money] NULL,
	[UserNum] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[GeneralHouseRate] [money] NULL,
	[NonGeneralHouseRate] [money] NULL,
	[GeneralHouseArea] [money] NULL,
	[NonGeneralHouseArea] [money] NULL,
	[CwPrice] [money] NULL,
	[GeneralHouseCoverArea] [money] NULL,
	[NoGeneralHouseCoverArea] [money] NULL,
	[OtherArea] [money] NULL,
	[OtherRate] [money] NULL,
	[OtherCoverArea] [money] NULL,
	[GeneralHouseCoverAreaRate] [money] NULL,
	[NoGeneralHouseCoverAreaRate] [money] NULL,
	[OtherCoverAreaRate] [money] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTINDEXCOMPARE] PRIMARY KEY CLUSTERED 
(
	[HkbProductIndexCompareGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HkbProductIndexHistory
CREATE TABLE [dbo].[cb_HkbProductIndexHistory](
	[HkbProductIndexHistoryGUID] [uniqueidentifier] NOT NULL,
	[ProductIndexGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[BuildTerraArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[BuildNum] [money] NULL,
	[FloorNum] [money] NULL,
	[CellNum] [money] NULL,
	[UserNum] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[GeneralHouseRate] [money] NULL,
	[NonGeneralHouseRate] [money] NULL,
	[GeneralHouseArea] [money] NULL,
	[NonGeneralHouseArea] [money] NULL,
	[CwPrice] [money] NULL,
	[GeneralHouseCoverArea] [money] NULL,
	[NoGeneralHouseCoverArea] [money] NULL,
	[OtherArea] [money] NULL,
	[OtherRate] [money] NULL,
	[OtherCoverArea] [money] NULL,
	[GeneralHouseCoverAreaRate] [money] NULL,
	[NoGeneralHouseCoverAreaRate] [money] NULL,
	[OtherCoverAreaRate] [money] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTINDEXHISTORY] PRIMARY KEY CLUSTERED 
(
	[HkbProductIndexHistoryGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HkbProductIndexWork
CREATE TABLE [dbo].[cb_HkbProductIndexWork](
	[ProductIndexGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[BuildTerraArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[BuildNum] [money] NULL,
	[FloorNum] [money] NULL,
	[CellNum] [money] NULL,
	[UserNum] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[GeneralHouseRate] [money] NULL,
	[GeneralHouseArea] [money] NULL,
	[NonGeneralHouseRate] [money] NULL,
	[NonGeneralHouseArea] [money] NULL,
	[CwPrice] [money] NULL,
	[GeneralHouseCoverArea] [money] NULL,
	[NoGeneralHouseCoverArea] [money] NULL,
	[OtherArea] [money] NULL,
	[OtherRate] [money] NULL,
	[OtherCoverArea] [money] NULL,
	[GeneralHouseCoverAreaRate] [money] NULL,
	[NoGeneralHouseCoverAreaRate] [money] NULL,
	[OtherCoverAreaRate] [money] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTINDEXWORK] PRIMARY KEY CLUSTERED 
(
	[ProductIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HkbProductWork
CREATE TABLE [dbo].[cb_HkbProductWork](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[ProductName] [varchar](40) NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[IsSale] [tinyint] NULL,
	[IsCYFT] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[Remarks] [text] NULL,
	[OccupyArea] [money] NULL,
	[OccupyRate] [money] NULL,
	[SaleArea] [money] NULL,
	[SaleRate] [money] NULL,
	[BuildArea] [money] NULL,
	[BuildRate] [money] NULL,
	[InnerArea] [money] NULL,
	[InnerRate] [money] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[PreNoSaleArea] [money] NULL,
	[CWPrice] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
 CONSTRAINT [PK_CB_HKBPRODUCTWORK] PRIMARY KEY CLUSTERED 
(
	[ProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HkbProjectIndexCompare
CREATE TABLE [dbo].[cb_HkbProjectIndexCompare](
	[HkbProjectIndexCompareGUID] [uniqueidentifier] NOT NULL,
	[ProjectIndexGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[AllTerraArea] [money] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[RoadArea] [money] NULL,
	[SightArea] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
	[YdlhArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[FactCubageRate] [money] NULL,
	[CwNum] [money] NULL,
	[UpperCwNum] [money] NULL,
	[UnderCwNum] [money] NULL,
	[ZjLength] [money] NULL,
	[GateNum] [money] NULL,
	[RjArea] [money] NULL,
	[RjAreaRate] [money] NULL,
	[YjArea] [money] NULL,
	[YjAreaRate] [money] NULL,
	[SjArea] [money] NULL,
	[SjAreaRate] [money] NULL,
	[QwsLength] [money] NULL,
	[QwdLength] [money] NULL,
	[QwqLength] [money] NULL,
	[QwrlLength] [money] NULL,
	[Kbsrj] [money] NULL,
	[Pdfrj] [money] NULL,
	[PdfNum] [money] NULL,
	[Fdjgl] [money] NULL,
	[WfQty] [money] NULL,
	[TfQty] [money] NULL,
	[WyQty] [money] NULL,
 CONSTRAINT [PK_CB_HKBPROJECTINDEXCOMPARE] PRIMARY KEY CLUSTERED 
(
	[HkbProjectIndexCompareGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HkbProjectIndexHistory
CREATE TABLE [dbo].[cb_HkbProjectIndexHistory](
	[HkbProjectIndexHistoryGUID] [uniqueidentifier] NOT NULL,
	[ProjectIndexGUID] [uniqueidentifier] NULL,
	[HkbApproveGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[AllTerraArea] [money] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[RoadArea] [money] NULL,
	[SightArea] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
	[YdlhArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[FactCubageRate] [money] NULL,
	[CwNum] [money] NULL,
	[UpperCwNum] [money] NULL,
	[UnderCwNum] [money] NULL,
	[ZjLength] [money] NULL,
	[GateNum] [money] NULL,
	[RjArea] [money] NULL,
	[RjAreaRate] [money] NULL,
	[YjArea] [money] NULL,
	[YjAreaRate] [money] NULL,
	[SjArea] [money] NULL,
	[SjAreaRate] [money] NULL,
	[QwsLength] [money] NULL,
	[QwdLength] [money] NULL,
	[QwqLength] [money] NULL,
	[QwrlLength] [money] NULL,
	[Kbsrj] [money] NULL,
	[Pdfrj] [money] NULL,
	[PdfNum] [money] NULL,
	[Fdjgl] [money] NULL,
	[WfQty] [money] NULL,
	[TfQty] [money] NULL,
	[WyQty] [money] NULL,
 CONSTRAINT [PK_CB_HKBPROJECTINDEXHISTORY] PRIMARY KEY CLUSTERED 
(
	[HkbProjectIndexHistoryGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HkbProjectIndexWork
CREATE TABLE [dbo].[cb_HkbProjectIndexWork](
	[ProjectIndexGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[AllTerraArea] [money] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[RoadArea] [money] NULL,
	[SightArea] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
	[YdlhArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[FactCubageRate] [money] NULL,
	[CwNum] [money] NULL,
	[UpperCwNum] [money] NULL,
	[UnderCwNum] [money] NULL,
	[ZjLength] [money] NULL,
	[GateNum] [money] NULL,
	[RjArea] [money] NULL,
	[RjAreaRate] [money] NULL,
	[YjArea] [money] NULL,
	[YjAreaRate] [money] NULL,
	[SjArea] [money] NULL,
	[SjAreaRate] [money] NULL,
	[QwsLength] [money] NULL,
	[QwdLength] [money] NULL,
	[QwqLength] [money] NULL,
	[QwrlLength] [money] NULL,
	[Kbsrj] [money] NULL,
	[Pdfrj] [money] NULL,
	[PdfNum] [money] NULL,
	[Fdjgl] [money] NULL,
	[WfQty] [money] NULL,
	[TfQty] [money] NULL,
	[WyQty] [money] NULL,
 CONSTRAINT [PK_CB_HKBPROJECTINDEXWORK] PRIMARY KEY CLUSTERED 
(
	[ProjectIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HsCfDtl
CREATE TABLE [dbo].[cb_HsCfDtl](
	[HsCfDtlGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[FtProductGUID] [uniqueidentifier] NULL,
	[FtAmount] [money] NULL,
	[CostCode] [varchar](100) NULL,
	[ProjCode] [varchar](100) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_HsCost
CREATE TABLE [dbo].[cb_HsCost](
	[HsCostGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IsEndCost] [tinyint] NULL,
	[FtMode] [varchar](16) NULL,
	[IsJianAn] [tinyint] NULL,
	[IsForecast] [tinyint] NULL,
	[IsEndForecast] [tinyint] NULL,
	[ForecastCost] [decimal](19, 6) NULL,
	[Remarks] [varchar](max) NULL,
	[JsCost] [money] NULL,
	[CSFtMode] [varchar](50) NULL,
	[HsFtMode] [varchar](50) NULL,
	[CSFtRate] [money] NULL,
	[HsFtRate] [money] NULL,
	[GetMode] [varchar](20) NULL,
	[CsjcState] [tinyint] NULL,
	[CsjcCode] [varchar](100) NULL,
	[CsjcName] [varchar](50) NULL,
	[XsUnit] [varchar](20) NULL,
	[GclUnit] [varchar](20) NULL,
 CONSTRAINT [PrimaryKey] PRIMARY KEY NONCLUSTERED 
(
	[HsCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HsCost_CsHistory
CREATE TABLE [dbo].[cb_HsCost_CsHistory](
	[ProductCostGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[EstimateFoundation] [varchar](100) NULL,
	[Unit] [varchar](50) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [decimal](19, 6) NULL,
	[UnitCost] [money] NULL,
	[SaleCost] [money] NULL,
	[Remarks] [text] NULL,
	[VersionGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[IsEndForecast] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HsCost_CsProduct
CREATE TABLE [dbo].[cb_HsCost_CsProduct](
	[ProductCostGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[EstimateFoundation] [varchar](100) NULL,
	[Unit] [varchar](50) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [decimal](19, 6) NULL,
	[UnitCost] [money] NULL,
	[SaleCost] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HsCost_CsVersion
CREATE TABLE [dbo].[cb_HsCost_CsVersion](
	[VersionGUID] [uniqueidentifier] NULL,
	[VersionName] [varchar](50) NULL,
	[BuGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[SaveDate] [datetime] NULL,
	[SaveBy] [varchar](50) NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HsCost_HsProduct
CREATE TABLE [dbo].[cb_HsCost_HsProduct](
	[ProductCostGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[EstimateFoundation] [varchar](100) NULL,
	[Unit] [varchar](50) NULL,
	[Qty] [money] NULL,
	[Price] [money] NULL,
	[Amount] [decimal](19, 6) NULL,
	[UnitCost] [money] NULL,
	[SaleCost] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HsCost_HtcfProduct
CREATE TABLE [dbo].[cb_HsCost_HtcfProduct](
	[ProductCostGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[HsCostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[HsCost] [decimal](14, 4) NULL,
 CONSTRAINT [PK_cb_HsCost_HtcfProduct] PRIMARY KEY NONCLUSTERED 
(
	[ProductCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtFileType
CREATE TABLE [dbo].[cb_HtFileType](
	[HtFileTypeGUID] [uniqueidentifier] NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[HtFileTypeShortCode] [varchar](200) NULL,
	[HtFileTypeCode] [varchar](200) NULL,
	[HtFileTypeName] [varchar](200) NULL,
	[IsMust] [int] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[HtClassName] [varchar](100) NULL,
	[IsSys] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[HtFileTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtType
CREATE TABLE [dbo].[cb_HtType](
	[HtTypeGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeShortCode] [varchar](10) NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtTypeShortName] [varchar](40) NULL,
	[HtTypeName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[AlterWarnRate] [money] NULL,
	[PayWarnRate] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[Remarks] [text] NULL,
	[HTProcessGUID] [uniqueidentifier] NULL,
	[FhtProcessGUID] [uniqueidentifier] NULL,
	[FDdhtProcessGUID] [uniqueidentifier] NULL,
	[IsContractPG] [tinyint] NULL,
	[IsCostControl] [tinyint] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsKzProgressPayment] [tinyint] NULL,
	[HTProcessGUIDList] [text] NULL,
	[FhtProcessGUIDList] [text] NULL,
	[FDdhtProcessGUIDList] [text] NULL,
 CONSTRAINT [PK_cb_HtType] PRIMARY KEY CLUSTERED 
(
	[HtTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_HtTypeAmountSet
CREATE TABLE [dbo].[cb_HtTypeAmountSet](
	[HtTypeAmountSetGUID] [uniqueidentifier] NOT NULL,
	[HtTypeStageGUID] [uniqueidentifier] NULL,
	[HtTypeAmountMin] [money] NULL,
	[HtTypeAmountMax] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[HtTypeAmountSetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtTypeFKRateSet
CREATE TABLE [dbo].[cb_HtTypeFKRateSet](
	[HtTypeRateSetGUID] [uniqueidentifier] NOT NULL,
	[HtTypeAmountSetGUID] [uniqueidentifier] NULL,
	[HtTypeFKRate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[HtTypeRateSetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtTypePGStage
CREATE TABLE [dbo].[cb_HtTypePGStage](
	[HtTypeStageGUID] [uniqueidentifier] NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[HtTypeStageName] [varchar](50) NULL,
	[HtTypeStageCode] [tinyint] NULL,
	[Rate] [money] NULL,
	[PGQDType] [tinyint] NULL,
	[FKType] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[HtTypeStageGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtTypePGStageQu
CREATE TABLE [dbo].[cb_HtTypePGStageQu](
	[HTTypeStageQuGUID] [uniqueidentifier] NOT NULL,
	[HtTypeStageGUID] [uniqueidentifier] NULL,
	[QuDefineGUID] [uniqueidentifier] NULL,
	[QuDefineName] [varchar](40) NULL,
	[Rate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[HTTypeStageQuGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_HtType_History
CREATE TABLE [dbo].[cb_HtType_History](
	[HtTypeGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeShortCode] [varchar](10) NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtTypeShortName] [varchar](40) NULL,
	[HtTypeName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[AlterWarnRate] [money] NULL,
	[PayWarnRate] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[Remarks] [text] NULL,
	[HTProcessGUID] [uniqueidentifier] NULL,
	[FhtProcessGUID] [uniqueidentifier] NULL,
	[FDdhtProcessGUID] [uniqueidentifier] NULL,
	[HTProcessGUIDList] [text] NULL,
	[FhtProcessGUIDList] [text] NULL,
	[FDdhtProcessGUIDList] [text] NULL,
 CONSTRAINT [PK_cb_HtType_History] PRIMARY KEY CLUSTERED 
(
	[HtTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_IMy2NcZJCallRecInfo
CREATE TABLE [dbo].[cb_IMy2NcZJCallRecInfo](
	[RecordID] [uniqueidentifier] NULL,
	[RecordTime] [datetime] NULL,
	[RecordByName] [varchar](100) NULL,
	[NcProt] [varchar](500) NULL,
	[State] [varchar](20) NULL,
	[Info] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_InvestAnalysis
CREATE TABLE [dbo].[cb_InvestAnalysis](
	[InvestAnalysisGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[PhaseName] [varchar](40) NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[DevotionJh] [money] NULL,
	[SumDevotionJh] [money] NULL,
	[DevotionSj] [money] NULL,
	[SumDevotionSj] [money] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_InvestAnalysis] PRIMARY KEY CLUSTERED 
(
	[InvestAnalysisGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Invoice
CREATE TABLE [dbo].[cb_Invoice](
	[InvoiceGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveUnitName] [varchar](100) NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[VouchType] [varchar](50) NULL,
	[Invotype] [varchar](50) NULL,
	[InvoNO] [varchar](200) NULL,
	[FundType] [varchar](50) NULL,
	[FundName] [varchar](50) NULL,
	[YwType] [varchar](50) NULL,
	[InvoiceAmount] [money] NULL,
	[PrevVoucherBalance] [money] NULL,
	[AfterVoucherBalance] [money] NULL,
	[Jbr] [varchar](20) NULL,
	[JbDate] [datetime] NULL,
	[ApproveState] [varchar](50) NULL,
	[Shr] [varchar](50) NULL,
	[ShDate] [datetime] NULL,
	[IsEnterVoucher] [varchar](50) NULL,
	[FinanceRegisterDate] [datetime] NULL,
	[FinanceExportDate] [datetime] NULL,
	[IsGenerateFail] [tinyint] NULL,
	[FailReason] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[ExcludingTaxInvoiceAmount] [money] NULL,
	[ReceivingMode] [varchar](10) NULL,
	[BuyerCompanyBankNNum] [nvarchar](260) NULL,
	[SellerCompanyBankNNum] [nvarchar](260) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_InvoiceItem
CREATE TABLE [dbo].[cb_InvoiceItem](
	[InvoiceItemGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveUnitName] [varchar](100) NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[VouchType] [varchar](50) NULL,
	[Invotype] [varchar](50) NULL,
	[InvoNO] [varchar](50) NULL,
	[FundType] [varchar](50) NULL,
	[FundName] [varchar](50) NULL,
	[YwType] [varchar](50) NULL,
	[InvoiceAmount] [money] NULL,
	[PrevVoucherBalance] [money] NULL,
	[AfterVoucherBalance] [money] NULL,
	[Jbr] [varchar](20) NULL,
	[JbDate] [datetime] NULL,
	[ApproveState] [varchar](50) NULL,
	[Shr] [varchar](50) NULL,
	[ShDate] [datetime] NULL,
	[IsEnterVoucher] [varchar](50) NULL,
	[FinanceRegisterDate] [datetime] NULL,
	[FinanceExportDate] [datetime] NULL,
	[IsGenerateFail] [tinyint] NULL,
	[FailReason] [varchar](200) NULL,
	[InvoiceType] [varchar](40) NULL,
	[InputTaxAmount] [money] NULL,
	[ExcludingTaxInvoiceAmount] [money] NULL,
	[InvoiceDate] [datetime] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[Code] [varchar](32) NULL,
	[BuyerCompanyBankNNum] [nvarchar](260) NULL,
	[SellerCompanyBankNNum] [nvarchar](260) NULL,
	[FtMode] [int] NULL,
	[OfficialInvoiceItemGUID] [uniqueidentifier] NULL,
	[Remarks] [varchar](1000) NULL,
	[SourceType] [nvarchar](50) NULL,
 CONSTRAINT [PK_cb_InvoiceItem] PRIMARY KEY CLUSTERED 
(
	[InvoiceItemGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_InvoiceItemDetail
CREATE TABLE [dbo].[cb_InvoiceItemDetail](
	[InvoiceItemDetailGUID] [uniqueidentifier] NOT NULL,
	[InvoiceItemGUID] [uniqueidentifier] NOT NULL,
	[TaxableServiceName] [varchar](100) NULL,
	[ExcludingTaxInvoiceAmount] [money] NULL,
	[TaxRate] [money] NULL,
	[InputTaxAmount] [money] NULL,
	[InvoiceAmount] [money] NULL,
	[Specification] [nvarchar](32) NULL,
	[Unit] [nvarchar](8) NULL,
	[Quantity] [money] NULL,
	[Price] [decimal](18, 6) NULL,
	[IsSimpleCollect] [int] NULL,
	[TaxableCategory] [varchar](50) NULL,
 CONSTRAINT [PK_InvoiceItemDetail] PRIMARY KEY CLUSTERED 
(
	[InvoiceItemDetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_InvoiceItemDetailCf
CREATE TABLE [dbo].[cb_InvoiceItemDetailCf](
	[CfDtlGUID] [uniqueidentifier] NOT NULL,
	[CfDtlCode] [varchar](500) NULL,
	[CfDtlName] [varchar](500) NULL,
	[IsEnd] [int] NULL,
	[Level] [int] NULL,
	[InvoiceItemDetailGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[Proj2LicenseGUID] [uniqueidentifier] NULL,
	[Scale] [decimal](18, 6) NULL,
	[TaxRate] [money] NULL,
	[ExcludingTaxInvoiceAmount] [money] NULL,
	[InputTaxAmount] [money] NULL,
	[InvoiceAmount] [money] NULL,
	[ParentGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[CfDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_InvoiceItemOfHTFKApply
CREATE TABLE [dbo].[cb_InvoiceItemOfHTFKApply](
	[InvoiceItemGUID] [uniqueidentifier] NULL,
	[HTFKApplyGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ListItem
CREATE TABLE [dbo].[cb_ListItem](
	[ListItemGUID] [uniqueidentifier] NOT NULL,
	[ListTemplateGUID] [uniqueidentifier] NULL,
	[ListItemName] [varchar](400) NULL,
	[ListItemShortCode] [varchar](10) NULL,
	[ListItemCode] [varchar](200) NULL,
	[ParentItemCode] [varchar](100) NULL,
	[IsEnable] [int] NULL,
	[Remark] [varchar](4000) NULL,
	[Position] [varchar](400) NULL,
	[ProjFeature] [varchar](1200) NULL,
	[JobContent] [varchar](400) NULL,
	[Unit] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IsEnd] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[ListItemGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ListTemplate
CREATE TABLE [dbo].[cb_ListTemplate](
	[ListTemplateGUID] [uniqueidentifier] NOT NULL,
	[ListName] [varchar](40) NULL,
	[ListShortCode] [varchar](10) NULL,
	[ListCode] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[IsEnable] [int] NULL,
	[Remark] [varchar](4000) NULL,
PRIMARY KEY CLUSTERED 
(
	[ListTemplateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Loan
CREATE TABLE [dbo].[cb_Loan](
	[LoanGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[Subject] [varchar](100) NULL,
	[LoanCode] [varchar](40) NULL,
	[ApplyState] [varchar](16) NULL,
	[AppliedBy] [uniqueidentifier] NULL,
	[AppliedByName] [varchar](20) NULL,
	[ApplyBUGUID] [uniqueidentifier] NULL,
	[ApplyDate] [datetime] NULL,
	[PayMode] [varchar](16) NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[ReceiveProviderName] [varchar](100) NULL,
	[BankName] [varchar](100) NULL,
	[BankAccounts] [varchar](50) NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[LoanAmount_Bz] [money] NULL,
	[LoanAmount] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[BalanceAmount] [money] NULL,
	[RemainAmount_Bz] [money] NULL,
	[RemainAmount] [money] NULL,
	[IsAccount] [tinyint] NULL,
	[Remarks] [text] NULL,
	[IfLocked] [tinyint] NULL,
	[PayState] [varchar](16) NULL,
	[LoanTypeGUID] [uniqueidentifier] NULL,
	[IfCqbyj] [tinyint] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[FinishDateTime] [datetime] NULL,
	[ApproveBy] [datetime] NULL,
	[LoanTypeName] [varchar](50) NULL,
	[Rate] [money] NULL,
	[LoanType] [varchar](10) NULL,
	[ReceiverName] [varchar](20) NULL,
	[ReceiverGUID] [uniqueidentifier] NULL,
	[ReceiverBankName] [varchar](100) NULL,
	[ReceiverBankAccounts] [varchar](50) NULL,
	[ReceiveType] [varchar](10) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_LoanBalanceApply
CREATE TABLE [dbo].[cb_LoanBalanceApply](
	[LoanBalanceApplyGUID] [uniqueidentifier] NULL,
	[LoanGUID] [uniqueidentifier] NULL,
	[RefType] [varchar](50) NULL,
	[LoanAmount_Bz] [money] NULL,
	[BalanceAmount_Bz] [money] NULL,
	[RemainAmount_Bz] [money] NULL,
	[CurBalanceAmount_Bz] [money] NULL,
	[RefGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_LoanReturn
CREATE TABLE [dbo].[cb_LoanReturn](
	[LoanReturnGUID] [uniqueidentifier] NULL,
	[LoanGUID] [uniqueidentifier] NULL,
	[ReturnCode] [varchar](50) NULL,
	[ReturnedBy] [varchar](20) NULL,
	[ReturnDate] [datetime] NULL,
	[ReturnAmount] [money] NULL,
	[Remarks] [text] NULL,
	[PayMode] [varchar](50) NULL,
	[FinanceRegisterDate] [datetime] NULL,
	[FailReason] [varchar](200) NULL,
	[IsGenerateFail] [tinyint] NULL,
	[ReturnedGUID] [uniqueidentifier] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_MBCBProfitRate
CREATE TABLE [dbo].[cb_MBCBProfitRate](
	[CBProfitRateGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[PlanSaleAmount] [money] NULL,
	[TargetCost] [money] NULL,
	[PubBuildFtCost] [money] NULL,
	[SaleTaxExtra] [money] NULL,
	[SaleTaxExtraRate] [money] NULL,
	[LandIncrementTax] [money] NULL,
	[BusinessTax] [money] NULL,
	[BusinessTaxRate] [money] NULL,
	[PlanProfit] [money] NULL,
	[PlanProfitRate] [money] NULL,
	[TargetStage2ProjectGUID] [uniqueidentifier] NULL,
	[IsNew] [bit] NULL,
 CONSTRAINT [PK_CB_MBCBPROFITRATE] PRIMARY KEY CLUSTERED 
(
	[CBProfitRateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Memory
CREATE TABLE [dbo].[cb_Memory](
	[GUID] [uniqueidentifier] NOT NULL,
	[UserGUID] [uniqueidentifier] NULL,
	[Key] [nvarchar](max) NULL,
	[Value] [nvarchar](max) NULL,
 CONSTRAINT [PK_cb_Memory] PRIMARY KEY CLUSTERED 
(
	[GUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_MonthPlan
CREATE TABLE [dbo].[cb_MonthPlan](
	[MonthPlanGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[MonthPlanName] [varchar](80) NULL,
	[PlanMonth] [varchar](7) NULL,
	[ZdDate] [datetime] NULL,
	[Zdr] [varchar](20) NULL,
	[ReportState] [varchar](10) NULL,
	[ApproveAmount] [money] NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ApproveState] [varchar](10) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_MonthPlanDtl
CREATE TABLE [dbo].[cb_MonthPlanDtl](
	[MonthPlanDtlGUID] [uniqueidentifier] NOT NULL,
	[MonthPlanGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[PlanDate] [datetime] NULL,
	[PlanAmount] [money] NULL,
	[ApproveAmount] [money] NULL,
	[YfDate] [datetime] NULL,
	[FkPlanType] [varchar](20) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[RefYfAmount] [money] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](20) NULL,
	[HTFKPlanName] [varchar](120) NULL,
	[PlanAmount_Bz] [money] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[ProjectName] [varchar](4000) NULL,
	[JhfkRemarks] [varchar](max) NULL,
	[OtherDeduct] [money] NULL,
	[HtYgAlertAmount] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumPayRate] [money] NULL,
	[YjSumPayRate] [money] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[SumPayAmount] [money] NULL,
	[ProjType] [varchar](20) NULL,
	[NextPlanAmount1] [money] NULL,
	[NextPlanAmount2] [money] NULL,
	[NextPlanAmount3] [money] NULL,
	[NextPlanAmount4] [money] NULL,
	[NextPlanAmount5] [money] NULL,
	[ForNowPayRate] [money] NULL,
	[ForNowScheduleRate] [money] NULL,
	[RemainAmount] [money] NULL,
	[SumYfNotPayAmount] [money] NULL,
	[YfProvider] [varchar](200) NULL,
	[MonthScheduleAmount] [money] NULL,
	[YsDeptGUID] [uniqueidentifier] NULL,
	[Ysr] [varchar](100) NULL,
	[YsrGUID] [uniqueidentifier] NULL,
	[ZjPlanCbqPay] [varchar](20) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_MonthPlanDtlExec
CREATE TABLE [dbo].[cb_MonthPlanDtlExec](
	[MonthPlanDtlExecGUID] [uniqueidentifier] NOT NULL,
	[MonthPlanGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[PlanDate] [datetime] NULL,
	[PlanAmount] [money] NULL,
	[ApproveAmount] [money] NULL,
	[YfDate] [datetime] NULL,
	[FkPlanType] [varchar](20) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[RefYfAmount] [money] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](20) NULL,
	[HTFKPlanName] [varchar](120) NULL,
	[PlanAmount_Bz] [money] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[ProjectName] [varchar](4000) NULL,
	[JhfkRemarks] [varchar](max) NULL,
	[OtherDeduct] [money] NULL,
	[HtYgAlertAmount] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumPayRate] [money] NULL,
	[YjSumPayRate] [money] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[SumPayAmount] [money] NULL,
	[ProjType] [varchar](20) NULL,
	[NextPlanAmount1] [money] NULL,
	[NextPlanAmount2] [money] NULL,
	[NextPlanAmount3] [money] NULL,
	[NextPlanAmount4] [money] NULL,
	[NextPlanAmount5] [money] NULL,
	[ForNowPayRate] [money] NULL,
	[ForNowScheduleRate] [money] NULL,
	[RemainAmount] [money] NULL,
	[SumYfNotPayAmount] [money] NULL,
	[YfProvider] [varchar](200) NULL,
	[MonthScheduleAmount] [money] NULL,
	[YsDeptGUID] [uniqueidentifier] NULL,
	[Ysr] [varchar](100) NULL,
	[YsrGUID] [uniqueidentifier] NULL,
	[ZjPlanCbqPay] [varchar](20) NULL,
	[ApproveState] [varchar](50) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_MonthPlanDtlExec_insertLogDtl
CREATE TABLE [dbo].[cb_MonthPlanDtlExec_insertLogDtl](
	[MonthPlanDtlExecGUID] [uniqueidentifier] NOT NULL,
	[MonthPlanGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[PlanDate] [datetime] NULL,
	[PlanAmount] [money] NULL,
	[ApproveAmount] [money] NULL,
	[YfDate] [datetime] NULL,
	[FkPlanType] [varchar](20) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[RefYfAmount] [money] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](20) NULL,
	[HTFKPlanName] [varchar](120) NULL,
	[PlanAmount_Bz] [money] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[ProjectName] [varchar](4000) NULL,
	[JhfkRemarks] [varchar](max) NULL,
	[OtherDeduct] [money] NULL,
	[HtYgAlertAmount] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumPayRate] [money] NULL,
	[YjSumPayRate] [money] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[SumPayAmount] [money] NULL,
	[ProjType] [varchar](20) NULL,
	[NextPlanAmount1] [money] NULL,
	[NextPlanAmount2] [money] NULL,
	[NextPlanAmount3] [money] NULL,
	[NextPlanAmount4] [money] NULL,
	[NextPlanAmount5] [money] NULL,
	[ForNowPayRate] [money] NULL,
	[ForNowScheduleRate] [money] NULL,
	[RemainAmount] [money] NULL,
	[SumYfNotPayAmount] [money] NULL,
	[YfProvider] [varchar](200) NULL,
	[MonthScheduleAmount] [money] NULL,
	[YsDeptGUID] [uniqueidentifier] NULL,
	[Ysr] [varchar](100) NULL,
	[YsrGUID] [uniqueidentifier] NULL,
	[ZjPlanCbqPay] [varchar](20) NULL,
	[ApproveState] [varchar](50) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_MonthPlanDtl_deleteLogDtl
CREATE TABLE [dbo].[cb_MonthPlanDtl_deleteLogDtl](
	[MonthPlanDtlGUID] [uniqueidentifier] NOT NULL,
	[MonthPlanGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[PlanDate] [datetime] NULL,
	[PlanAmount] [money] NULL,
	[ApproveAmount] [money] NULL,
	[YfDate] [datetime] NULL,
	[FkPlanType] [varchar](20) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[HtDtAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[RefYfAmount] [money] NULL,
	[HTFKPlanGUID] [uniqueidentifier] NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](20) NULL,
	[HTFKPlanName] [varchar](120) NULL,
	[PlanAmount_Bz] [money] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[ProjectName] [varchar](4000) NULL,
	[JhfkRemarks] [varchar](max) NULL,
	[OtherDeduct] [money] NULL,
	[HtYgAlertAmount] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumPayRate] [money] NULL,
	[YjSumPayRate] [money] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[SumPayAmount] [money] NULL,
	[ProjType] [varchar](20) NULL,
	[NextPlanAmount1] [money] NULL,
	[NextPlanAmount2] [money] NULL,
	[NextPlanAmount3] [money] NULL,
	[NextPlanAmount4] [money] NULL,
	[NextPlanAmount5] [money] NULL,
	[ForNowPayRate] [money] NULL,
	[ForNowScheduleRate] [money] NULL,
	[RemainAmount] [money] NULL,
	[SumYfNotPayAmount] [money] NULL,
	[YfProvider] [varchar](200) NULL,
	[MonthScheduleAmount] [money] NULL,
	[YsDeptGUID] [uniqueidentifier] NULL,
	[Ysr] [varchar](100) NULL,
	[YsrGUID] [uniqueidentifier] NULL,
	[ZjPlanCbqPay] [varchar](20) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_NCYSSee
CREATE TABLE [dbo].[cb_NCYSSee](
	[NCYSSeeGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjCode] [varchar](200) NULL,
	[ProjName] [varchar](2000) NULL,
	[BuName] [varchar](200) NULL,
	[CostCode] [varchar](200) NULL,
	[CostName] [varchar](200) NULL,
	[BudgetAmount] [money] NULL,
	[BudgetYear] [varchar](200) NULL,
	[BudgetMonth] [varchar](200) NULL,
	[HTFKApplyAmount] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Partner
CREATE TABLE [dbo].[cb_Partner](
	[PartnerGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[PartnerCode] [varchar](40) NULL,
	[PartnerShortName] [varchar](40) NULL,
	[PartnerName] [varchar](100) NULL,
	[PartnerTypeNameList] [text] NULL,
	[Corporation] [varchar](20) NULL,
	[EstablishDate] [datetime] NULL,
	[RegisterFund] [varchar](16) NULL,
	[EnterpriseProperty] [varchar](16) NULL,
	[QualificationRegister] [varchar](16) NULL,
	[LicenceCode] [varchar](30) NULL,
	[Address] [varchar](100) NULL,
	[ContactBy] [varchar](20) NULL,
	[ContactPhone] [varchar](16) NULL,
	[BankName] [varchar](40) NULL,
	[BankAccounts] [varchar](30) NULL,
	[OtherRemarks] [text] NULL,
	[ReviewDate] [datetime] NULL,
	[ReviewedBy] [varchar](100) NULL,
	[OwnerCooperation] [varchar](16) NULL,
	[LatelyProjQuality] [varchar](16) NULL,
	[WorkEquipment] [varchar](16) NULL,
	[EmployeeStructure] [varchar](100) NULL,
	[FixedAssets] [varchar](16) NULL,
	[NetAssets] [varchar](16) NULL,
	[OweDebts] [varchar](16) NULL,
	[FloatingFund] [varchar](16) NULL,
	[LatelyHonor] [varchar](100) NULL,
	[Credit] [varchar](100) NULL,
	[ReviewIdea] [text] NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
 CONSTRAINT [PK_cb_Partner] PRIMARY KEY CLUSTERED 
(
	[PartnerGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Partner2Item
CREATE TABLE [dbo].[cb_Partner2Item](
	[Partner2ItemGUID] [uniqueidentifier] NOT NULL,
	[PartnerGUID] [uniqueidentifier] NOT NULL,
	[ItemGUID] [uniqueidentifier] NOT NULL,
	[Price] [money] NULL,
	[QuoteDate] [datetime] NULL,
	[ApproveState] [varchar](10) NULL,
	[IfHistory] [tinyint] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_Partner2Item] PRIMARY KEY CLUSTERED 
(
	[Partner2ItemGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Partner2Type
CREATE TABLE [dbo].[cb_Partner2Type](
	[BUGUID] [uniqueidentifier] NOT NULL,
	[PartnerGUID] [uniqueidentifier] NOT NULL,
	[PartnerTypeCode] [varchar](100) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_PartnerType
CREATE TABLE [dbo].[cb_PartnerType](
	[PartnerTypeGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[PartnerTypeShortCode] [varchar](10) NULL,
	[PartnerTypeCode] [varchar](100) NULL,
	[PartnerTypeShortName] [varchar](40) NULL,
	[PartnerTypeName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
 CONSTRAINT [PK_cb_PartnerType] PRIMARY KEY CLUSTERED 
(
	[PartnerTypeGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Pay
CREATE TABLE [dbo].[cb_Pay](
	[PayGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[VouchGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[PayMode] [varchar](16) NULL,
	[PayBankName] [varchar](30) NULL,
	[PayDate] [datetime] NULL,
	[PayAmount] [money] NULL,
	[Currency] [varchar](10) NULL,
	[Rate] [money] NULL,
	[PayAmount1] [money] NULL,
	[Ye] [money] NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[Status] [varchar](10) NULL,
	[PayRemarks] [text] NULL,
	[PrePayGUID] [uniqueidentifier] NULL,
	[FinanceJsMode] [varchar](30) NULL,
	[FinanceJsCode] [nvarchar](50) NULL,
	[PreCfPayGUID] [uniqueidentifier] NULL,
	[DcAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[LoanGUID] [uniqueidentifier] NULL,
	[HTFKApplyGUID] [uniqueidentifier] NULL,
	[FundSource] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ApproveState] [varchar](10) NULL,
	[ExcludingTaxPayAmount1] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_PlanAnalyseDept
CREATE TABLE [dbo].[cb_PlanAnalyseDept](
	[BUGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[Year] [int] NULL,
	[Month01] [money] NULL,
	[Month02] [money] NULL,
	[Month03] [money] NULL,
	[Month04] [money] NULL,
	[Month05] [money] NULL,
	[Month06] [money] NULL,
	[Month07] [money] NULL,
	[Month08] [money] NULL,
	[Month09] [money] NULL,
	[Month10] [money] NULL,
	[Month11] [money] NULL,
	[Month12] [money] NULL,
	[AmountType] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_PlanAnalysePhotoTime
CREATE TABLE [dbo].[cb_PlanAnalysePhotoTime](
	[BUGUID] [uniqueidentifier] NULL,
	[PhotoDateTime] [datetime] NULL,
	[PhotoUserGUID] [uniqueidentifier] NULL,
	[PhotoUserName] [varchar](20) NULL,
	[Year] [int] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_PlanAnalyseProj
CREATE TABLE [dbo].[cb_PlanAnalyseProj](
	[BUGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[Year] [int] NULL,
	[Month01] [money] NULL,
	[Month02] [money] NULL,
	[Month03] [money] NULL,
	[Month04] [money] NULL,
	[Month05] [money] NULL,
	[Month06] [money] NULL,
	[Month07] [money] NULL,
	[Month08] [money] NULL,
	[Month09] [money] NULL,
	[Month10] [money] NULL,
	[Month11] [money] NULL,
	[Month12] [money] NULL,
	[AmountType] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Product
CREATE TABLE [dbo].[cb_Product](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[ProductName] [varchar](40) NULL,
	[OccupyArea] [money] NULL,
	[SaleArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[Remarks] [text] NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[OccupyRate] [money] NULL,
	[BuildRate] [money] NULL,
	[SaleRate] [money] NULL,
	[InnerRate] [money] NULL,
	[ProductState] [varchar](20) NULL,
	[UnderArea] [money] NULL,
	[UpperArea] [money] NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[CpFtBuildRate] [money] NULL,
	[CpFtOccupyRate] [money] NULL,
	[CpFtSaleRate] [money] NULL,
	[CpFtInnerRate] [money] NULL,
	[DateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[WorkGuid] [uniqueidentifier] NULL,
	[PlanSaleDate] [datetime] NULL,
	[PreGetSaleDate] [datetime] NULL,
	[PlanSalePeriod] [int] NULL,
	[RentableArea] [money] NULL,
	[FinishSaleArea] [money] NULL,
	[FinishSaleDate] [datetime] NULL,
	[PreNoSaleArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[IsCYFT] [tinyint] NULL,
	[CpFtBuildAmount] [money] NULL,
	[CpFtOccupyAmount] [money] NULL,
	[CpFtSaleAmount] [money] NULL,
	[CpFtInnerAmount] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
	[IntegrationTime] [datetime] NULL,
	[IsCar] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Product20220706
CREATE TABLE [dbo].[cb_Product20220706](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[ProductName] [varchar](40) NULL,
	[OccupyArea] [money] NULL,
	[SaleArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[Remarks] [text] NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[OccupyRate] [money] NULL,
	[BuildRate] [money] NULL,
	[SaleRate] [money] NULL,
	[InnerRate] [money] NULL,
	[ProductState] [varchar](20) NULL,
	[UnderArea] [money] NULL,
	[UpperArea] [money] NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[CpFtBuildRate] [money] NULL,
	[CpFtOccupyRate] [money] NULL,
	[CpFtSaleRate] [money] NULL,
	[CpFtInnerRate] [money] NULL,
	[DateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[WorkGuid] [uniqueidentifier] NULL,
	[PlanSaleDate] [datetime] NULL,
	[PreGetSaleDate] [datetime] NULL,
	[PlanSalePeriod] [int] NULL,
	[RentableArea] [money] NULL,
	[FinishSaleArea] [money] NULL,
	[FinishSaleDate] [datetime] NULL,
	[PreNoSaleArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[IsCYFT] [tinyint] NULL,
	[CpFtBuildAmount] [money] NULL,
	[CpFtOccupyAmount] [money] NULL,
	[CpFtSaleAmount] [money] NULL,
	[CpFtInnerAmount] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
	[IntegrationTime] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductApply
CREATE TABLE [dbo].[cb_ProductApply](
	[ProductApplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ApplyCode] [varchar](100) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[Applyer] [varchar](20) NULL,
	[ApplyDate] [datetime] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveDate] [datetime] NULL,
	[Remarks] [text] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[FinishState] [varchar](20) NULL,
 CONSTRAINT [PK_cb_ProductApply] PRIMARY KEY CLUSTERED 
(
	[ProductApplyGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductApplyDtl
CREATE TABLE [dbo].[cb_ProductApplyDtl](
	[ProductApplyDtlGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProductApplyGUID] [uniqueidentifier] NULL,
	[BackDate] [datetime] NULL,
	[ApplyQty] [money] NULL,
	[ApplyPrice] [money] NULL,
	[ApplyAmount] [money] NULL,
	[Remarks] [text] NULL,
	[PlanQty] [money] NULL,
 CONSTRAINT [PK_cb_ProductApplyDtl] PRIMARY KEY NONCLUSTERED 
(
	[ProductApplyDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductBudgetDtl
CREATE TABLE [dbo].[cb_ProductBudgetDtl](
	[ProductBudgetDtlGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProductProjectCode] [varchar](100) NULL,
	[BudgetPrice] [money] NULL,
	[BudgetQty] [money] NULL,
	[BudgetAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDate] [datetime] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_ProductBudgetDtl] PRIMARY KEY CLUSTERED 
(
	[ProductBudgetDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductBulidCost
CREATE TABLE [dbo].[cb_ProductBulidCost](
	[ProductBulidCostGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[BulidCost] [money] NULL,
	[CrossProjCost] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductBulidCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductBulidDtCost
CREATE TABLE [dbo].[cb_ProductBulidDtCost](
	[ProductBulidCostGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[BulidCost] [money] NULL,
	[CrossProjCost] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductBulidCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductCbFtRule
CREATE TABLE [dbo].[cb_ProductCbFtRule](
	[ProductCbFtRuleGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[FtRate] [decimal](12, 8) NULL,
	[FtAmount] [money] NULL,
	[FtMode] [varchar](16) NULL,
 CONSTRAINT [PK_cb_ProductCbFtRule] PRIMARY KEY NONCLUSTERED 
(
	[ProductCbFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductCost
CREATE TABLE [dbo].[cb_ProductCost](
	[ProductCostGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](200) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IfEndCost] [tinyint] NULL,
	[EstimateCost] [money] NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ProductDynamicProfit
CREATE TABLE [dbo].[cb_ProductDynamicProfit](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[PlanSaleAmount] [money] NULL,
	[SaleTaxExtraRate] [money] NULL,
	[PubBuildFtCost] [money] NULL,
	[LandIncrementTax] [money] NULL,
	[BusinessTaxRate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductFtRule
CREATE TABLE [dbo].[cb_ProductFtRule](
	[ProductFtRuleGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[FtRate] [decimal](15, 10) NULL,
	[CostType] [varchar](50) NULL,
	[FtMode] [varchar](16) NULL,
 CONSTRAINT [PK_cb_ProductFtRule] PRIMARY KEY CLUSTERED 
(
	[ProductFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductFtRuleState
CREATE TABLE [dbo].[cb_ProductFtRuleState](
	[ProductFtRuleStateGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[IsLockedTarget] [tinyint] NULL,
	[IsLockedDynamic] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductFtRuleStateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductHistory
CREATE TABLE [dbo].[cb_ProductHistory](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[ProductName] [varchar](40) NOT NULL,
	[OccupyArea] [money] NULL,
	[SaleArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[Remarks] [text] NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[OccupyRate] [money] NULL,
	[BuildRate] [money] NULL,
	[SaleRate] [money] NULL,
	[InnerRate] [money] NULL,
	[ProductState] [varchar](20) NULL,
	[UnderArea] [money] NULL,
	[UpperArea] [money] NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[CpFtBuildRate] [money] NULL,
	[CpFtOccupyRate] [money] NULL,
	[CpFtSaleRate] [money] NULL,
	[CpFtInnerRate] [money] NULL,
	[DateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[WorkGuid] [uniqueidentifier] NULL,
	[PlanSaleDate] [datetime] NULL,
	[PreGetSaleDate] [datetime] NULL,
	[PlanSalePeriod] [int] NULL,
	[RentableArea] [money] NULL,
	[FinishSaleArea] [money] NULL,
	[FinishSaleDate] [datetime] NULL,
	[PreNoSaleArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[IsCYFT] [tinyint] NULL,
	[CpFtBuildAmount] [money] NULL,
	[CpFtOccupyAmount] [money] NULL,
	[CpFtSaleAmount] [money] NULL,
	[CpFtInnerAmount] [money] NULL,
	[SumBuildArea] [money] NULL,
	[SumSaleArea] [money] NULL,
 CONSTRAINT [PK_CB_PRODUCTHISTORY] PRIMARY KEY NONCLUSTERED 
(
	[ProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductIndex
CREATE TABLE [dbo].[cb_ProductIndex](
	[ProductIndexGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ProductName] [varchar](40) NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[FtRemark] [nvarchar](2000) NULL,
	[DtFtRemark] [nvarchar](2000) NULL,
	[IsCar] [int] NOT NULL,
	[FtRate] [money] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductIndex20220706
CREATE TABLE [dbo].[cb_ProductIndex20220706](
	[ProductIndexGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ProductName] [varchar](40) NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[FtRemark] [nvarchar](2000) NULL,
	[DtFtRemark] [nvarchar](2000) NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ProductOrder
CREATE TABLE [dbo].[cb_ProductOrder](
	[ProductOrderGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ProductPlanGUID] [uniqueidentifier] NULL,
	[OrderCode] [varchar](100) NULL,
	[Zdr] [varchar](20) NULL,
	[ZdDate] [datetime] NULL,
	[Remarks] [text] NULL,
	[JbrGUID] [varchar](50) NULL,
	[JbDeptGUID] [varchar](50) NULL,
	[State] [varchar](10) NULL,
 CONSTRAINT [PK_cb_ProductOrder] PRIMARY KEY CLUSTERED 
(
	[ProductOrderGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductOrderDtl
CREATE TABLE [dbo].[cb_ProductOrderDtl](
	[ProductOrderDtlGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductOrderGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ApplyQty] [money] NULL,
	[ApplyPrice] [money] NULL,
	[ApplyAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_ProductOrderDtl] PRIMARY KEY NONCLUSTERED 
(
	[ProductOrderDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductPlan
CREATE TABLE [dbo].[cb_ProductPlan](
	[ProductPlanGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[PlanCode] [varchar](100) NULL,
	[PlanContent] [varchar](500) NULL,
	[Planer] [varchar](20) NULL,
	[PlanDate] [datetime] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ApproveDate] [datetime] NULL,
	[Remarks] [text] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
	[StockState] [varchar](20) NULL,
 CONSTRAINT [PK_cb_ProductPlan] PRIMARY KEY CLUSTERED 
(
	[ProductPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductPlan2Apply
CREATE TABLE [dbo].[cb_ProductPlan2Apply](
	[ProductPlan2ApplyGUID] [uniqueidentifier] NOT NULL,
	[ProductPlanGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductApplyGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK__cb_ProductPlan2Apply] PRIMARY KEY CLUSTERED 
(
	[ProductPlan2ApplyGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductPlanDtl
CREATE TABLE [dbo].[cb_ProductPlanDtl](
	[ProductPlanDtlGUID] [uniqueidentifier] NULL,
	[ProductPlanGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[PlanQty] [money] NULL,
	[PlanPrice] [money] NULL,
	[PlanAmount] [money] NULL,
	[StockState] [varchar](20) NULL,
	[Remarks] [ntext] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductPlanDtl2ApplyDtl
CREATE TABLE [dbo].[cb_ProductPlanDtl2ApplyDtl](
	[ProductPlanDtlGUID] [uniqueidentifier] NULL,
	[ProductPlanGUID] [uniqueidentifier] NULL,
	[ProductApplyGUID] [uniqueidentifier] NULL,
	[PlanQty] [money] NULL,
	[ProductApplyDtlGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ProductPlayPlan
CREATE TABLE [dbo].[cb_ProductPlayPlan](
	[ProductPlayPlanGUID] [uniqueidentifier] NOT NULL,
	[PlanCode] [varchar](200) NULL,
	[PlanName] [varchar](100) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ZdDate] [datetime] NULL,
	[Zdr] [varchar](100) NULL,
 CONSTRAINT [PK_cb_ProductPlayPlan] PRIMARY KEY CLUSTERED 
(
	[ProductPlayPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductPlayPlanDtl
CREATE TABLE [dbo].[cb_ProductPlayPlanDtl](
	[ProductPlayPlanDtlGUID] [uniqueidentifier] NOT NULL,
	[ProductPlayPlanGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_ProductPlayPlanDtl] PRIMARY KEY NONCLUSTERED 
(
	[ProductPlayPlanDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductPlayPlanProvider
CREATE TABLE [dbo].[cb_ProductPlayPlanProvider](
	[ProductPlayPlanProviderGUID] [uniqueidentifier] NOT NULL,
	[ProductPlayPlanDtlGUID] [uniqueidentifier] NULL,
	[ProviderGUID] [uniqueidentifier] NULL,
	[ProductPlayPlanGUID] [uniqueidentifier] NULL,
	[Price] [money] NULL,
	[BUGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_ProductPlayPlanProvider] PRIMARY KEY CLUSTERED 
(
	[ProductPlayPlanProviderGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProductProject
CREATE TABLE [dbo].[cb_ProductProject](
	[ProductProjectGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductProjectShortCode] [varchar](10) NULL,
	[ProductProjectCode] [varchar](100) NULL,
	[ProductProjectShortName] [varchar](40) NULL,
	[ProductProjectName] [varchar](400) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_ProductProject] PRIMARY KEY CLUSTERED 
(
	[ProductProjectGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductSupply
CREATE TABLE [dbo].[cb_ProductSupply](
	[ProductSupplyGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ProductOrderGUID] [uniqueidentifier] NULL,
	[SupplyCode] [varchar](100) NULL,
	[Zdr] [varchar](20) NULL,
	[ZdDate] [datetime] NULL,
	[Remarks] [text] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[JbDeptGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_ProductSupply] PRIMARY KEY CLUSTERED 
(
	[ProductSupplyGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductSupplyDtl
CREATE TABLE [dbo].[cb_ProductSupplyDtl](
	[ProductSupplyDtlGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProductSupplyGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProductTypeGUID] [uniqueidentifier] NULL,
	[ProductProjectCode] [varchar](100) NULL,
	[SupplyQty] [money] NULL,
	[SupplyPrice] [money] NULL,
	[SupplyAmount] [money] NULL,
	[SupplyDate] [datetime] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_ProductSupplyDtl] PRIMARY KEY NONCLUSTERED 
(
	[ProductSupplyDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ProductTargetProfit
CREATE TABLE [dbo].[cb_ProductTargetProfit](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[PlanSaleAmount] [money] NULL,
	[SaleTaxExtraRate] [money] NULL,
	[PubBuildFtCost] [money] NULL,
	[LandIncrementTax] [money] NULL,
	[BusinessTaxRate] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectCbftRule
CREATE TABLE [dbo].[cb_ProjectCbftRule](
	[ProjectCbftRuleGUID] [uniqueidentifier] NOT NULL,
	[FtProjGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[FtMode] [varchar](16) NULL,
	[FtRate] [decimal](12, 8) NULL,
	[FtAmount] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](40) NULL,
	[LastFtMode] [varchar](16) NULL,
 CONSTRAINT [PK_cb_ProjectCbftRule] PRIMARY KEY CLUSTERED 
(
	[ProjectCbftRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectCommand
CREATE TABLE [dbo].[cb_ProjectCommand](
	[ProjectCommandGuid] [uniqueidentifier] NOT NULL,
	[AlterGuid] [uniqueidentifier] NULL,
	[CommandCode] [varchar](800) NULL,
	[CommandContext] [varchar](2000) NULL,
	[CommandDate] [datetime] NULL,
	[ContractGuid] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[ProjectCommandGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectCommandFile
CREATE TABLE [dbo].[cb_ProjectCommandFile](
	[ProjectCommandFileGuid] [uniqueidentifier] NOT NULL,
	[ProjectCommandGuid] [uniqueidentifier] NULL,
	[FileName] [varchar](50) NULL,
	[PageNum] [int] NULL,
	[DocGuidList] [varchar](2000) NULL,
	[DocNameList] [varchar](2000) NULL,
PRIMARY KEY CLUSTERED 
(
	[ProjectCommandFileGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectCost
CREATE TABLE [dbo].[cb_ProjectCost](
	[ProjectCostGUID] [uniqueidentifier] NOT NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](200) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IfEndCost] [tinyint] NULL,
	[DtCost] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_ProjectCost] PRIMARY KEY NONCLUSTERED 
(
	[ProjectCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectExtend
CREATE TABLE [dbo].[cb_ProjectExtend](
	[ProjectExtendGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[JAnState] [varchar](10) NULL,
 CONSTRAINT [PK_cb_ProjectExtend] PRIMARY KEY NONCLUSTERED 
(
	[ProjectExtendGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectLockBudget
CREATE TABLE [dbo].[cb_ProjectLockBudget](
	[ProjectGUID] [uniqueidentifier] NULL,
	[IfLock] [tinyint] NULL,
	[CheckOutUserGUID] [uniqueidentifier] NULL,
	[CheckOutUserName] [varchar](50) NULL,
	[CheckOutTime] [datetime] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ProjectTaskInfo
CREATE TABLE [dbo].[cb_ProjectTaskInfo](
	[ProjGUID] [uniqueidentifier] NOT NULL,
	[TaskType] [varchar](100) NOT NULL,
	[ExecuteTime] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ProjGUID] ASC,
	[TaskType] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_PublicProductDtFtRule
CREATE TABLE [dbo].[cb_PublicProductDtFtRule](
	[PublicProductFtRuleGUID] [uniqueidentifier] NOT NULL,
	[OutProjGUID] [uniqueidentifier] NULL,
	[OutProductGUID] [uniqueidentifier] NULL,
	[InCostCode] [varchar](100) NULL,
	[InProjGUID] [uniqueidentifier] NULL,
	[InProductGUID] [uniqueidentifier] NULL,
	[FtMode] [varchar](200) NULL,
	[FtRate] [decimal](27, 13) NULL,
	[FtAmount] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[PublicProductFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_PublicProductFtRule
CREATE TABLE [dbo].[cb_PublicProductFtRule](
	[PublicProductFtRuleGUID] [uniqueidentifier] NOT NULL,
	[OutProjGUID] [uniqueidentifier] NULL,
	[OutProductGUID] [uniqueidentifier] NULL,
	[InCostCode] [varchar](100) NULL,
	[InProjGUID] [uniqueidentifier] NULL,
	[InProductGUID] [uniqueidentifier] NULL,
	[FtMode] [varchar](200) NULL,
	[FtRate] [decimal](27, 13) NULL,
	[FtAmount] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[PublicProductFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ResLock
CREATE TABLE [dbo].[cb_ResLock](
	[ResGUID] [uniqueidentifier] NOT NULL,
	[FailureTime] [datetime] NOT NULL,
	[UserName] [varchar](20) NOT NULL,
	[Res] [varchar](800) NOT NULL,
	[LockInfo] [nvarchar](500) NULL,
UNIQUE NONCLUSTERED 
(
	[Res] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_StockCost
CREATE TABLE [dbo].[cb_StockCost](
	[StockCostGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostShortName] [varchar](40) NULL,
	[CostCode] [varchar](100) NULL,
	[CostName] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[IsEndCost] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
	[EntryAmount] [money] NULL,
	[DeliveryAmount] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_StockCostCf
CREATE TABLE [dbo].[cb_StockCostCf](
	[StockCostUseGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[StockCostGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
	[RefType] [varchar](16) NULL,
	[HappenTime] [datetime] NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
 CONSTRAINT [PK_cb_StockCostCf] PRIMARY KEY CLUSTERED 
(
	[StockCostUseGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_StockCostUse
CREATE TABLE [dbo].[cb_StockCostUse](
	[StockCostUseGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[StockCostGUID] [uniqueidentifier] NOT NULL,
	[CfAmount] [money] NULL,
	[RefType] [varchar](16) NULL,
	[HappenTime] [datetime] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_StockCost_History
CREATE TABLE [dbo].[cb_StockCost_History](
	[StockCostGUID] [uniqueidentifier] NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostShortName] [varchar](40) NULL,
	[CostCode] [varchar](100) NULL,
	[CostName] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[IsEndCost] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
	[EntryAmount] [money] NULL,
	[DeliveryAmount] [money] NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_StockOut
CREATE TABLE [dbo].[cb_StockOut](
	[StockOutGUID] [uniqueidentifier] NOT NULL,
	[StockOutName] [varchar](200) NULL,
	[StockCostGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
	[HappenTime] [datetime] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[ApproveState] [varchar](10) NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_StockOut] PRIMARY KEY CLUSTERED 
(
	[StockOutGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_TCRSetChgCostAmountLog
CREATE TABLE [dbo].[cb_TCRSetChgCostAmountLog](
	[LogGUID] [uniqueidentifier] NOT NULL,
	[ChgGUID] [uniqueidentifier] NULL,
	[NCProjCode] [varchar](100) NULL,
	[ProjCode] [varchar](100) NULL,
	[ProjName] [varchar](200) NULL,
	[NCCostCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[CostName] [varchar](200) NULL,
	[PayAmountBeforeChg] [money] NULL,
	[SumPayAmountBeforeChg] [money] NULL,
	[NoTaxPayAmountBeforeChg] [money] NULL,
	[NoTaxSumPayAmountBeforeChg] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[LogGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TCRSetChgLog
CREATE TABLE [dbo].[cb_TCRSetChgLog](
	[ChgGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[RatioSetBeforeChg] [varchar](2000) NULL,
	[RatioSetAfterChg] [varchar](2000) NULL,
	[ChgDate] [datetime] NULL,
	[ChgByGUID] [uniqueidentifier] NULL,
	[ChgBy] [varchar](20) NULL,
PRIMARY KEY CLUSTERED 
(
	[ChgGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetStage2Cost
CREATE TABLE [dbo].[cb_TargetStage2Cost](
	[TargetStage2CostGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjCode] [varchar](100) NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[CostName] [varchar](40) NULL,
	[CostCode] [varchar](100) NULL,
	[IfEndCost] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
	[TargetCost] [money] NULL,
	[AdjustCost] [money] NULL,
	[TargetStage2ProjectGUID] [uniqueidentifier] NULL,
	[InputTaxTargetCost] [money] NOT NULL,
	[ExcludingTaxTargetCost] [money] NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[InputTaxAdjustCost] [money] NOT NULL,
	[ExcludingTaxAdjustCost] [money] NOT NULL,
 CONSTRAINT [PK_CB_TARGETSTAGE2COST] PRIMARY KEY CLUSTERED 
(
	[TargetStage2CostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetStage2Project
CREATE TABLE [dbo].[cb_TargetStage2Project](
	[TargetStage2ProjectGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NOT NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[Applyer] [varchar](20) NULL,
	[ApplyerGUID] [uniqueidentifier] NULL,
	[ApproveDate] [datetime] NULL,
	[TargetStageSetGUID] [uniqueidentifier] NOT NULL,
	[CsfaGUID] [uniqueidentifier] NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[IsCurrTargetStage] [bit] NULL,
	[CsVersionGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[TargetStage2ProjectGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetStage2Tzys
CREATE TABLE [dbo].[cb_TargetStage2Tzys](
	[TargetStage2TzysGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[VersionGUID] [uniqueidentifier] NULL,
	[VersionName] [varchar](200) NULL,
	[ImportDate] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[TargetStage2TzysGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetStage2TzysCost
CREATE TABLE [dbo].[cb_TargetStage2TzysCost](
	[TargetStage2TzysCostGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[CostName] [varchar](200) NULL,
	[CostCode] [varchar](200) NULL,
	[TagetCostAmount] [money] NULL,
	[TaxRate] [money] NULL,
	[TagetCostNoTaxAmount] [money] NULL,
	[TaxAmount] [money] NULL,
	[CostRemark] [varchar](2000) NULL,
PRIMARY KEY CLUSTERED 
(
	[TargetStage2TzysCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetStageSet
CREATE TABLE [dbo].[cb_TargetStageSet](
	[TargetStageSetGUID] [uniqueidentifier] NOT NULL,
	[TargetStageSetName] [varchar](50) NULL,
	[SetVersion] [varchar](20) NULL,
	[Remark] [varchar](8000) NULL,
	[RowIndex] [int] NULL,
 CONSTRAINT [PK_CB_TARGETSTAGESET] PRIMARY KEY CLUSTERED 
(
	[TargetStageSetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TargetTF
CREATE TABLE [dbo].[cb_TargetTF](
	[TargetTFGUID] [uniqueidentifier] NULL,
	[TargetCostGUID] [uniqueidentifier] NULL,
	[TFDate] [datetime] NULL,
	[TFRate] [money] NULL,
	[TFAmount] [money] NULL,
	[Remark] [ntext] NULL,
	[ifTz] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Task
CREATE TABLE [dbo].[cb_Task](
	[TaskGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NOT NULL,
	[TaskShortCode] [varchar](10) NOT NULL,
	[TaskCode] [varchar](100) NOT NULL,
	[TaskShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[TaskClass] [varchar](16) NULL,
	[TaskType] [varchar](16) NULL,
	[JbDeptCode] [varchar](500) NULL,
	[Jbr] [varchar](20) NULL,
	[BeginDateJh] [datetime] NULL,
	[EndDateJh] [datetime] NULL,
	[BeginDateSj] [datetime] NULL,
	[EndDateSj] [datetime] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_Task] PRIMARY KEY CLUSTERED 
(
	[TaskGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Task2Contract
CREATE TABLE [dbo].[cb_Task2Contract](
	[TaskGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_Tax
CREATE TABLE [dbo].[cb_Tax](
	[TaxGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[TaxName] [varchar](40) NULL,
	[TaxRate] [money] NULL,
	[TaxRange] [text] NULL,
	[TaxObligor] [text] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_Tax] PRIMARY KEY CLUSTERED 
(
	[TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_TaxCompanyYear
CREATE TABLE [dbo].[cb_TaxCompanyYear](
	[cbTaxCompanyYearGUID] [uniqueidentifier] NOT NULL,
	[CompanyGuid] [uniqueidentifier] NULL,
	[TaxYear] [int] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveByGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[cbTaxCompanyYearGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TaxManageValue
CREATE TABLE [dbo].[cb_TaxManageValue](
	[cbTaxManageGUID] [uniqueidentifier] NOT NULL,
	[TaxCode] [varchar](100) NULL,
	[CompanyGuid] [uniqueidentifier] NULL,
	[TaxYear] [int] NULL,
	[TaxMonth] [int] NULL,
	[TaxValue] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[cbTaxManageGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TaxMng
CREATE TABLE [dbo].[cb_TaxMng](
	[TaxMngGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxRate] [money] NULL,
	[TaxAmount] [money] NULL,
	[TaxState] [varchar](10) NULL,
	[TaxedBy] [varchar](20) NULL,
	[TaxDate] [datetime] NULL,
	[CancelTaxDate] [datetime] NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_TaxMng] PRIMARY KEY CLUSTERED 
(
	[TaxMngGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_TaxYearValue
CREATE TABLE [dbo].[cb_TaxYearValue](
	[TaxYearValueGUID] [uniqueidentifier] NOT NULL,
	[TaxCode] [varchar](100) NULL,
	[CompanyGuid] [uniqueidentifier] NULL,
	[TaxYear] [int] NULL,
	[YearTotal] [money] NULL,
	[Month1] [money] NULL,
	[Month2] [money] NULL,
	[Month3] [money] NULL,
	[Month4] [money] NULL,
	[Month5] [money] NULL,
	[Month6] [money] NULL,
	[Month7] [money] NULL,
	[Month8] [money] NULL,
	[Month9] [money] NULL,
	[Month10] [money] NULL,
	[Month11] [money] NULL,
	[Month12] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[TaxYearValueGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Tax_History
CREATE TABLE [dbo].[cb_Tax_History](
	[TaxGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[TaxName] [varchar](40) NULL,
	[TaxRate] [money] NULL,
	[TaxRange] [text] NULL,
	[TaxObligor] [text] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_Tax_History] PRIMARY KEY CLUSTERED 
(
	[TaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ThreeCostAmount
CREATE TABLE [dbo].[cb_ThreeCostAmount](
	[ThreeCostAmountGUID] [uniqueidentifier] NOT NULL,
	[DataTag] [varchar](20) NULL,
	[NCProjCode] [varchar](100) NULL,
	[ProjCode] [varchar](100) NULL,
	[ProjName] [varchar](200) NULL,
	[NCCostCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[CostName] [varchar](200) NULL,
	[PayAmount] [money] NULL,
	[SumPayAmount] [money] NULL,
	[SumPayAmount_Last] [money] NULL,
	[NoTaxPayAmount] [money] NULL,
	[NoTaxSumPayAmount] [money] NULL,
	[NoTaxSumPayAmount_Last] [money] NULL,
	[ModiDate] [datetime] NULL,
	[PayAmount_NowCost] [money] NULL,
	[NoTaxPayAmount_NowCost] [money] NULL,
	[PayAmount_Last] [money] NULL,
	[NoTaxPayAmount_Last] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[ThreeCostAmountGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ThreeCostAmountDayAddDetail
CREATE TABLE [dbo].[cb_ThreeCostAmountDayAddDetail](
	[TCADetailGUID] [uniqueidentifier] NOT NULL,
	[DataTag] [varchar](20) NULL,
	[YearMonth] [varchar](100) NULL,
	[NCProjCode] [varchar](100) NULL,
	[ProjCode] [varchar](100) NULL,
	[ProjName] [varchar](200) NULL,
	[CostCode] [varchar](100) NULL,
	[CostName] [varchar](200) NULL,
	[AddPayAmount] [money] NULL,
	[AddNoTaxPayAmount] [money] NULL,
	[TransDate] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[TCADetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ThreeCostRatioSet
CREATE TABLE [dbo].[cb_ThreeCostRatioSet](
	[RatioSetGUID] [uniqueidentifier] NOT NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[Remark] [text] NULL,
PRIMARY KEY CLUSTERED 
(
	[RatioSetGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ThreeCostRatioSetDetail
CREATE TABLE [dbo].[cb_ThreeCostRatioSetDetail](
	[RatioSetDetailGUID] [uniqueidentifier] NOT NULL,
	[RatioSetGUID] [uniqueidentifier] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[CostRatioValue] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[RatioSetDetailGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ValueAddedTax
CREATE TABLE [dbo].[cb_ValueAddedTax](
	[ValueAddedTaxGUID] [uniqueidentifier] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL,
	[TaxRate] [money] NULL,
	[Remark] [varchar](200) NULL,
	[Cgtimestamp] [timestamp] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
 CONSTRAINT [PK_cb_ValueAddedTax] PRIMARY KEY CLUSTERED 
(
	[ValueAddedTaxGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_VersionNC
CREATE TABLE [dbo].[cb_VersionNC](
	[VersionGUID] [uniqueidentifier] NOT NULL,
	[VersionType] [int] NULL,
	[ProjCode] [varchar](200) NULL,
	[VersionYear] [varchar](20) NULL,
	[VersionDate] [datetime] NULL,
	[BUGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[VersionGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_Voucher
CREATE TABLE [dbo].[cb_Voucher](
	[VouchGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[VouchType] [varchar](16) NULL,
	[VouchStatus] [varchar](20) NULL,
	[Invotype] [varchar](20) NULL,
	[InvoNO] [nvarchar](max) NULL,
	[Kpr] [varchar](20) NULL,
	[KpDate] [datetime] NULL,
	[Shr] [varchar](20) NULL,
	[ShDate] [datetime] NULL,
	[ApproveState] [varchar](10) NULL,
	[ReceiveUnitName] [varchar](400) NULL,
	[ReceiveBankName] [varchar](200) NULL,
	[FinanceJsMode] [varchar](30) NULL,
	[FinanceJsCode] [varchar](30) NULL,
	[FinanceRegisterDate] [datetime] NULL,
	[FinanceExportDate] [datetime] NULL,
	[VoucherGroup] [varchar](10) NULL,
	[VoucherNo] [varchar](20) NULL,
	[ObjID] [varchar](20) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[FtMode] [tinyint] NULL,
	[PayProviderGUID] [uniqueidentifier] NULL,
	[PayProviderName] [varchar](100) NULL,
	[ReceiveProviderGUID] [uniqueidentifier] NULL,
	[InvoiceAmount] [money] NULL,
	[PrevVoucherBalance] [money] NULL,
	[AfterVoucherBalance] [money] NULL,
	[IsEnterVoucher] [varchar](10) NULL,
	[FailReason] [varchar](200) NULL,
	[ReceiveBankAccounts] [varchar](50) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[PayAmount1] [money] NULL,
	[DcAmount] [money] NULL,
	[YwType] [varchar](20) NULL,
	[BxAmount] [money] NULL,
	[IsGenerateFail] [tinyint] NULL,
	[InputTaxAmount] [money] NULL,
	[ExcludingTaxInvoiceAmount] [money] NULL,
	[BuyerCompanyBankNNum] [nvarchar](260) NULL,
	[SellerCompanyBankNNum] [nvarchar](260) NULL,
	[IsFromERP253] [tinyint] NULL,
	[NCVoucherCode] [varchar](50) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_Warning
CREATE TABLE [dbo].[cb_Warning](
	[WarningGUID] [uniqueidentifier] NOT NULL,
	[CostGUID] [uniqueidentifier] NOT NULL,
	[ControlLevel] [tinyint] NOT NULL,
	[IndicatorType] [tinyint] NOT NULL,
	[IndicatorValue] [decimal](18, 2) NOT NULL,
	[Notification] [varchar](max) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_XjlFa
CREATE TABLE [dbo].[cb_XjlFa](
	[XjlFaGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NOT NULL,
	[XjlFaName] [varchar](80) NULL,
	[FaMonth] [varchar](7) NULL,
	[XjlTemplateGUID] [uniqueidentifier] NOT NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_XjlFa] PRIMARY KEY CLUSTERED 
(
	[XjlFaGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_XjlFaDtl
CREATE TABLE [dbo].[cb_XjlFaDtl](
	[XjlFaDtlGUID] [uniqueidentifier] NOT NULL,
	[XjlFaGUID] [uniqueidentifier] NOT NULL,
	[BillCode] [varchar](100) NULL,
	[BillShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[Direction] [varchar](10) NULL,
	[IfImportProject] [tinyint] NULL,
	[ProjectNameList] [text] NULL,
	[Sum1] [money] NULL,
	[Sum2] [money] NULL,
	[Sum3] [money] NULL,
	[Sum4] [money] NULL,
	[P1] [money] NULL,
	[P2] [money] NULL,
	[P3] [money] NULL,
	[P4] [money] NULL,
	[P5] [money] NULL,
	[P6] [money] NULL,
	[P7] [money] NULL,
	[P8] [money] NULL,
	[P9] [money] NULL,
	[P10] [money] NULL,
	[P11] [money] NULL,
	[P12] [money] NULL,
	[P13] [money] NULL,
	[P14] [money] NULL,
	[P15] [money] NULL,
	[P16] [money] NULL,
 CONSTRAINT [PK_cb_XjlFaDtl] PRIMARY KEY CLUSTERED 
(
	[XjlFaDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_XjlTemplate
CREATE TABLE [dbo].[cb_XjlTemplate](
	[XjlTemplateGUID] [uniqueidentifier] NOT NULL,
	[XjlTemplateName] [varchar](80) NULL,
	[IsSys] [tinyint] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_XjlTemplate] PRIMARY KEY CLUSTERED 
(
	[XjlTemplateGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_XjlTemplateDtl
CREATE TABLE [dbo].[cb_XjlTemplateDtl](
	[XjlTemplateDtlGUID] [uniqueidentifier] NOT NULL,
	[XjlTemplateGUID] [uniqueidentifier] NOT NULL,
	[BillShortCode] [varchar](10) NULL,
	[BillCode] [varchar](100) NULL,
	[BillShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[Direction] [varchar](10) NULL,
	[IfImportProject] [tinyint] NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_XjlTemplateDtl] PRIMARY KEY CLUSTERED 
(
	[XjlTemplateDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_YSTemp
CREATE TABLE [dbo].[cb_YSTemp](
	[tempguid] [uniqueidentifier] NOT NULL,
	[userguid] [uniqueidentifier] NULL,
	[temppcguid] [uniqueidentifier] NULL,
	[temptype] [int] NULL,
	[deptcode] [varchar](200) NULL,
	[deptname] [varchar](200) NULL,
	[jobcode] [varchar](200) NULL,
	[jobname] [varchar](200) NULL,
	[code_meatrics] [varchar](200) NULL,
	[name_meatrics] [varchar](200) NULL,
	[code_year] [varchar](200) NULL,
	[code_month] [varchar](200) NULL,
	[name_month] [varchar](200) NULL,
	[value] [varchar](200) NULL,
	[BUGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[tempguid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlter2Budget
CREATE TABLE [dbo].[cb_YgAlter2Budget](
	[YgAlter2BudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
	[ygAlterAdj] [money] NOT NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
	[InputTaxCfAmount] [money] NOT NULL,
	[ExcludingTaxygAlterAdj] [money] NOT NULL,
	[InputTaxygAlterAdj] [money] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlter2Budget_shenxbak20250612
CREATE TABLE [dbo].[cb_YgAlter2Budget_shenxbak20250612](
	[YgAlter2BudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[CfAmount] [money] NULL,
	[ygAlterAdj] [money] NOT NULL,
	[ExcludingTaxCfAmount] [money] NOT NULL,
	[InputTaxCfAmount] [money] NOT NULL,
	[ExcludingTaxygAlterAdj] [money] NOT NULL,
	[InputTaxygAlterAdj] [money] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlterAdjust
CREATE TABLE [dbo].[cb_YgAlterAdjust](
	[YgAlterAdjustGUID] [uniqueidentifier] NULL,
	[SUBJECT] [nvarchar](2000) NULL,
	[AdjustDate] [datetime] NULL,
	[Adjuster] [nvarchar](20) NULL,
	[AdjustAmount_Bz] [money] NULL,
	[AdjustAmount] [money] NULL,
	[Rate] [decimal](12, 8) NULL,
	[Bz] [nvarchar](50) NULL,
	[Remark] [nvarchar](2000) NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ExcludingTaxAdjustAmount] [money] NOT NULL,
	[ExcludingTaxAdjustAmount_Bz] [money] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlterAdjustDtl
CREATE TABLE [dbo].[cb_YgAlterAdjustDtl](
	[YgAlterAdjustDtlGUID] [uniqueidentifier] NULL,
	[YgAlterAdjustGUID] [uniqueidentifier] NULL,
	[ProjCode] [varchar](200) NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[CurAdjustAmount] [money] NULL,
	[ExcludingTaxCurAdjustAmount] [money] NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlterAdjustDtlWork
CREATE TABLE [dbo].[cb_YgAlterAdjustDtlWork](
	[YgAlterAdjustDtlGUID] [uniqueidentifier] NOT NULL,
	[YgAlterAdjustGUID] [uniqueidentifier] NULL,
	[ProjCode] [nvarchar](500) NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetGUID] [uniqueidentifier] NULL,
	[CurAdjustAmount] [money] NULL,
	[ExcludingTaxCurAdjustAmount] [money] NULL,
PRIMARY KEY CLUSTERED 
(
	[YgAlterAdjustDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlterAdjustWork
CREATE TABLE [dbo].[cb_YgAlterAdjustWork](
	[YgAlterAdjustGUID] [uniqueidentifier] NOT NULL,
	[SUBJECT] [nvarchar](2000) NULL,
	[AdjustDate] [datetime] NULL,
	[Adjuster] [nvarchar](30) NULL,
	[AdjustAmount_Bz] [money] NULL,
	[AdjustAmount] [money] NULL,
	[ExcludingTaxAdjustAmount] [money] NULL,
	[ExcludingTaxAdjustAmount_Bz] [money] NULL,
	[Rate] [decimal](18, 0) NULL,
	[Bz] [nvarchar](50) NULL,
	[Remark] [nvarchar](2000) NULL,
	[ApproveState] [nvarchar](10) NULL,
	[ApproveUser] [nvarchar](10) NULL,
	[ApproveTime] [datetime] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
PRIMARY KEY CLUSTERED 
(
	[YgAlterAdjustGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_YgAlterHistory
CREATE TABLE [dbo].[cb_YgAlterHistory](
	[YGAlterHistoryGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[XdDate] [datetime] NULL,
	[YGAlterAmount] [money] NULL,
	[BudgetInfo] [ntext] NULL,
	[Xdr] [varchar](16) NULL,
	[Remark] [text] NULL,
	[ExcludingTaxYGAlterAmount] [money] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_ZjPlan
CREATE TABLE [dbo].[cb_ZjPlan](
	[ZjPlanGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[PlanMonth] [varchar](10) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[IfEndCost] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
	[DtCost] [money] NULL,
	[YfsCost] [money] NULL,
	[PayCost] [money] NULL,
	[DfsCost] [money] NULL,
	[TargetAdjustCost] [money] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[P1a] [money] NULL,
	[P1b] [money] NULL,
	[P2a] [money] NULL,
	[P2b] [money] NULL,
	[P3a] [money] NULL,
	[P3b] [money] NULL,
	[P4a] [money] NULL,
	[P4b] [money] NULL,
	[P5a] [money] NULL,
	[P5b] [money] NULL,
	[P6a] [money] NULL,
	[P6b] [money] NULL,
	[P7a] [money] NULL,
	[P7b] [money] NULL,
	[P8a] [money] NULL,
	[P8b] [money] NULL,
	[P9a] [money] NULL,
	[P9b] [money] NULL,
	[P10a] [money] NULL,
	[P10b] [money] NULL,
	[P11a] [money] NULL,
	[P11b] [money] NULL,
	[P12a] [money] NULL,
	[P12b] [money] NULL,
	[P13a] [money] NULL,
	[P13b] [money] NULL,
	[P14a] [money] NULL,
	[P14b] [money] NULL,
	[P15a] [money] NULL,
	[P15b] [money] NULL,
	[P16a] [money] NULL,
	[P16b] [money] NULL,
	[P17a] [money] NULL,
	[P17b] [money] NULL,
	[P18a] [money] NULL,
	[P18b] [money] NULL,
	[P19a] [money] NULL,
	[P19b] [money] NULL,
	[P20a] [money] NULL,
	[P20b] [money] NULL,
	[P21a] [money] NULL,
	[P21b] [money] NULL,
	[P22a] [money] NULL,
	[P22b] [money] NULL,
	[P23a] [money] NULL,
	[P23b] [money] NULL,
	[P24a] [money] NULL,
	[P24b] [money] NULL,
	[P25a] [money] NULL,
	[P25b] [money] NULL,
	[P26a] [money] NULL,
	[P26b] [money] NULL,
	[P27a] [money] NULL,
	[P27b] [money] NULL,
	[P28a] [money] NULL,
	[P28b] [money] NULL,
	[P29a] [money] NULL,
	[P29b] [money] NULL,
	[P30a] [money] NULL,
	[P30b] [money] NULL,
	[P31a] [money] NULL,
	[P31b] [money] NULL,
	[P32a] [money] NULL,
	[P32b] [money] NULL,
	[P33a] [money] NULL,
	[P33b] [money] NULL,
	[P34a] [money] NULL,
	[P34b] [money] NULL,
	[P35a] [money] NULL,
	[P35b] [money] NULL,
	[P36a] [money] NULL,
	[P36b] [money] NULL,
	[P37a] [money] NULL,
	[P37b] [money] NULL,
	[P38a] [money] NULL,
	[P38b] [money] NULL,
	[P39a] [money] NULL,
	[P39b] [money] NULL,
	[P40a] [money] NULL,
	[P40b] [money] NULL,
	[P41a] [money] NULL,
	[P41b] [money] NULL,
	[P42a] [money] NULL,
	[P42b] [money] NULL,
	[P43a] [money] NULL,
	[P43b] [money] NULL,
	[P44a] [money] NULL,
	[P44b] [money] NULL,
	[P45a] [money] NULL,
	[P45b] [money] NULL,
	[P46a] [money] NULL,
	[P46b] [money] NULL,
	[P47a] [money] NULL,
	[P47b] [money] NULL,
	[P48a] [money] NULL,
	[P48b] [money] NULL,
	[P49a] [money] NULL,
	[P49b] [money] NULL,
	[P50a] [money] NULL,
	[P50b] [money] NULL,
	[P51a] [money] NULL,
	[P51b] [money] NULL,
	[P52a] [money] NULL,
	[P52b] [money] NULL,
	[P53a] [money] NULL,
	[P53b] [money] NULL,
	[P54a] [money] NULL,
	[P54b] [money] NULL,
	[P55a] [money] NULL,
	[P55b] [money] NULL,
	[P56a] [money] NULL,
	[P56b] [money] NULL,
	[P57a] [money] NULL,
	[P57b] [money] NULL,
	[P58a] [money] NULL,
	[P58b] [money] NULL,
	[P59a] [money] NULL,
	[P59b] [money] NULL,
	[P60a] [money] NULL,
	[P60b] [money] NULL,
	[P61a] [money] NULL,
	[P61b] [money] NULL,
	[P62a] [money] NULL,
	[P62b] [money] NULL,
	[P63a] [money] NULL,
	[P63b] [money] NULL,
	[P64a] [money] NULL,
	[P64b] [money] NULL,
	[P65a] [money] NULL,
	[P65b] [money] NULL,
	[P66a] [money] NULL,
	[P66b] [money] NULL,
	[P67a] [money] NULL,
	[P67b] [money] NULL,
	[P68a] [money] NULL,
	[P68b] [money] NULL,
	[P69a] [money] NULL,
	[P69b] [money] NULL,
	[P70a] [money] NULL,
	[P70b] [money] NULL,
	[P71a] [money] NULL,
	[P71b] [money] NULL,
	[P72a] [money] NULL,
	[P72b] [money] NULL,
	[P73a] [money] NULL,
	[P73b] [money] NULL,
	[P74a] [money] NULL,
	[P74b] [money] NULL,
	[P75a] [money] NULL,
	[P75b] [money] NULL,
	[P76a] [money] NULL,
	[P76b] [money] NULL,
	[P77a] [money] NULL,
	[P77b] [money] NULL,
	[P78a] [money] NULL,
	[P78b] [money] NULL,
	[P79a] [money] NULL,
	[P79b] [money] NULL,
	[P80a] [money] NULL,
	[P80b] [money] NULL,
	[P81a] [money] NULL,
	[P81b] [money] NULL,
	[P82a] [money] NULL,
	[P82b] [money] NULL,
	[P83a] [money] NULL,
	[P83b] [money] NULL,
	[P84a] [money] NULL,
	[P84b] [money] NULL,
	[P85a] [money] NULL,
	[P85b] [money] NULL,
	[P86a] [money] NULL,
	[P86b] [money] NULL,
	[P87a] [money] NULL,
	[P87b] [money] NULL,
	[P88a] [money] NULL,
	[P88b] [money] NULL,
	[P89a] [money] NULL,
	[P89b] [money] NULL,
	[P90a] [money] NULL,
	[P90b] [money] NULL,
	[P91a] [money] NULL,
	[P91b] [money] NULL,
	[P92a] [money] NULL,
	[P92b] [money] NULL,
	[P93a] [money] NULL,
	[P93b] [money] NULL,
	[P94a] [money] NULL,
	[P94b] [money] NULL,
	[P95a] [money] NULL,
	[P95b] [money] NULL,
	[P96a] [money] NULL,
	[P96b] [money] NULL,
	[P97a] [money] NULL,
	[P97b] [money] NULL,
	[P98a] [money] NULL,
	[P98b] [money] NULL,
	[P99a] [money] NULL,
	[P99b] [money] NULL,
	[P100a] [money] NULL,
	[P100b] [money] NULL,
	[P101a] [money] NULL,
	[P101b] [money] NULL,
	[P102a] [money] NULL,
	[P102b] [money] NULL,
	[P103a] [money] NULL,
	[P103b] [money] NULL,
	[P104a] [money] NULL,
	[P104b] [money] NULL,
	[P105a] [money] NULL,
	[P105b] [money] NULL,
	[P106a] [money] NULL,
	[P106b] [money] NULL,
	[P107a] [money] NULL,
	[P107b] [money] NULL,
	[P108a] [money] NULL,
	[P108b] [money] NULL,
	[P109a] [money] NULL,
	[P109b] [money] NULL,
	[P110a] [money] NULL,
	[P110b] [money] NULL,
	[P111a] [money] NULL,
	[P111b] [money] NULL,
	[P112a] [money] NULL,
	[P112b] [money] NULL,
	[P113a] [money] NULL,
	[P113b] [money] NULL,
	[P114a] [money] NULL,
	[P114b] [money] NULL,
	[P115a] [money] NULL,
	[P115b] [money] NULL,
	[P116a] [money] NULL,
	[P116b] [money] NULL,
	[P117a] [money] NULL,
	[P117b] [money] NULL,
	[P118a] [money] NULL,
	[P118b] [money] NULL,
	[P119a] [money] NULL,
	[P119b] [money] NULL,
	[P120a] [money] NULL,
	[P120b] [money] NULL,
	[Sum_Bcz] [money] NULL,
 CONSTRAINT [PK_cb_ZjPlan] PRIMARY KEY CLUSTERED 
(
	[ZjPlanGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_ZjPlanCompile
CREATE TABLE [dbo].[cb_ZjPlanCompile](
	[PlanCompileGUID] [uniqueidentifier] NOT NULL,
	[HTFKPlanGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[HTFkPlanType] [varchar](20) NULL,
	[HTFKPlanName] [varchar](200) NULL,
	[JhFKAmount] [money] NULL,
	[JhFKAmount_Bz] [money] NULL,
	[JhfkDate] [datetime] NULL,
	[Jbr] [varchar](20) NULL,
	[JbDeptName] [varchar](50) NULL,
	[ProjName] [varchar](200) NULL,
	[HtClass] [varchar](20) NULL,
	[CurrencyGUID] [uniqueidentifier] NULL,
	[Rate] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_ZjPlanDtl
CREATE TABLE [dbo].[cb_ZjPlanDtl](
	[ZjPlanDtlGUID] [uniqueidentifier] NOT NULL,
	[PlanMonth] [varchar](10) NULL,
	[ProjectCode] [varchar](100) NULL,
	[CostCode] [varchar](100) NULL,
	[Unit] [varchar](16) NULL,
	[Price] [money] NULL,
	[Qty] [money] NULL,
	[TfAmount] [money] NULL,
	[TfDate] [datetime] NULL,
	[Types] [varchar](16) NULL,
	[Remarks] [text] NULL,
	[ContractGUID] [uniqueidentifier] NULL,
	[ifTz] [tinyint] NULL,
 CONSTRAINT [PK_cb_ZjPlanDtl] PRIMARY KEY CLUSTERED 
(
	[ZjPlanDtlGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_budget20240823
CREATE TABLE [dbo].[cb_budget20240823](
	[BudgetGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[CostGUID] [uniqueidentifier] NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BudgetCode] [varchar](300) NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[BudgetAmount] [money] NULL,
	[UsedAmount] [money] NULL,
	[IsUseable] [tinyint] NULL,
	[FactCost] [money] NULL,
	[WorkGUID] [uniqueidentifier] NULL,
	[ExecuteDateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[ExecuteDate] [datetime] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[Remarks] [text] NULL,
	[RefName] [varchar](100) NULL,
	[IsZB] [tinyint] NULL,
	[CgType] [varchar](20) NULL,
	[CgCategoryGUID] [uniqueidentifier] NULL,
	[CgCategoryName] [varchar](100) NULL,
	[CZRelativelyDay] [int] NULL,
	[CZExecuteDate] [datetime] NULL,
	[TimeStampVer] [timestamp] NOT NULL,
	[SignupAmount] [decimal](18, 2) NULL,
	[AlterAmount] [decimal](18, 2) NULL,
	[PercentageOfAlter] [decimal](18, 2) NULL,
	[ExecutingBudgetGUID] [uniqueidentifier] NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [money] NOT NULL,
	[InputTaxBudgetAmount] [money] NOT NULL,
	[TaxGUID] [uniqueidentifier] NULL,
	[TaxName] [nvarchar](128) NOT NULL,
	[TaxRate] [decimal](18, 2) NOT NULL,
	[ExcludingTaxUsedAmount] [money] NOT NULL,
	[InputTaxUsedAmount] [money] NOT NULL,
	[UsedTaxRate] [decimal](18, 2) NOT NULL,
	[ValueAddedTaxGUID] [uniqueidentifier] NULL,
	[ExcludingTaxFactCost] [money] NOT NULL,
	[InputTaxFactCost] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[InputTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxSignupAmount] [money] NOT NULL,
	[ExcludingTaxAlterAmount] [money] NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [money] NOT NULL,
	[TaxableCategory] [varchar](50) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_budget_executing20240823
CREATE TABLE [dbo].[cb_budget_executing20240823](
	[ExecutingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[IsUseable] [bit] NOT NULL,
	[IsLocked] [bit] NOT NULL,
	[Remark] [varchar](500) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_budget_working20240823
CREATE TABLE [dbo].[cb_budget_working20240823](
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[BudgetName] [nvarchar](2000) NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[ProjectGUID] [uniqueidentifier] NOT NULL,
	[BudgetCode] [varchar](300) NOT NULL,
	[ParentCode] [varchar](300) NULL,
	[Level] [int] NULL,
	[IfEnd] [bit] NOT NULL,
	[BudgetAmount] [decimal](18, 2) NOT NULL,
	[HtTypeGUID] [uniqueidentifier] NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ReferenceTimeType] [int] NOT NULL,
	[RelativeTaskGUID] [uniqueidentifier] NULL,
	[EstimatedSignDate] [datetime] NULL,
	[SignupOffsetDays] [int] NULL,
	[SourceType] [varchar](50) NULL,
	[IsBiddingAndPurchasing] [bit] NOT NULL,
	[BiddingAndPurchasingDate] [datetime] NULL,
	[BiddingAndPurchasingOffsetDays] [int] NULL,
	[ModifyType] [int] NOT NULL,
	[Remark] [varchar](500) NULL,
	[ApproveState] [varchar](10) NULL,
	[OriginalBudgetAmount] [decimal](18, 2) NULL,
	[ExcludingTaxBudgetAmount] [decimal](18, 2) NOT NULL,
	[TaxRate] [decimal](18, 12) NOT NULL,
	[ExcludingTaxOriginalBudgetAmount] [decimal](18, 2) NOT NULL,
	[IsModuleYr] [int] NOT NULL,
	[AdjustAmount] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_budgetbill20250507
CREATE TABLE [dbo].[cb_budgetbill20250507](
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[Subject] [nvarchar](200) NULL,
	[UserGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NOT NULL,
	[ApproveState] [varchar](10) NOT NULL,
	[Remark] [nvarchar](500) NULL,
	[ProjectGUID] [uniqueidentifier] NULL,
	[Budget2CostWorkingProjectGuidList] [nvarchar](max) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_budgetbilldetail20240823
CREATE TABLE [dbo].[cb_budgetbilldetail20240823](
	[BudgetBillDetailGUID] [uniqueidentifier] NOT NULL,
	[BudgetBillGUID] [uniqueidentifier] NOT NULL,
	[WorkingBudgetGUID] [uniqueidentifier] NOT NULL,
	[ModifyType] [int] NOT NULL,
	[PlanAmountBeforeAdjust] [decimal](18, 2) NOT NULL,
	[PlanAmountAfterAdjust] [decimal](18, 2) NOT NULL,
	[ExcludingTaxPlanAmountBeforeAdjust] [decimal](18, 2) NOT NULL,
	[ExcludingTaxPlanAmountAfterAdjust] [decimal](18, 2) NOT NULL
) ON [PRIMARY]
GO

-- TABLE: cb_cdb_CommonIndex
CREATE TABLE [dbo].[cb_cdb_CommonIndex](
	[IndexGuid] [uniqueidentifier] NOT NULL,
	[TmplIndexGuid] [uniqueidentifier] NOT NULL,
	[IndexValue] [varchar](max) NOT NULL,
	[RefGuid] [uniqueidentifier] NULL,
	[ProjectState] [varchar](20) NULL,
	[Remarks] [varchar](1000) NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[IndexGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_cdb_CommonIndexTmpl
CREATE TABLE [dbo].[cb_cdb_CommonIndexTmpl](
	[IndexGuid] [uniqueidentifier] NOT NULL,
	[CostDbType] [tinyint] NOT NULL,
	[Name] [varchar](100) NOT NULL,
	[IndexUnit] [varchar](20) NULL,
	[Remarks] [varchar](1000) NULL,
	[IndexCode] [varchar](100) NOT NULL,
	[Level] [tinyint] NOT NULL,
	[IsLastLevel] [bit] NOT NULL,
	[IsEnabled] [bit] NOT NULL,
	[RefType] [tinyint] NOT NULL,
	[ParentCode] [varchar](100) NULL,
	[OptionsConfig] [varchar](max) NULL,
	[DataTypeConfig] [tinyint] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[IndexGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_cdb_CostDbInfo
CREATE TABLE [dbo].[cb_cdb_CostDbInfo](
	[CostDbInfoGuid] [uniqueidentifier] NOT NULL,
	[Name] [varchar](50) NOT NULL,
	[Code] [varchar](100) NOT NULL,
	[IsEnabled] [bit] NOT NULL,
	[Description] [varchar](4000) NOT NULL,
	[ExampleUrl] [varchar](1000) NULL,
	[DisplayOrder] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[CostDbInfoGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_cdb_CupIndex
CREATE TABLE [dbo].[cb_cdb_CupIndex](
	[IndexGuid] [uniqueidentifier] NOT NULL,
	[IndexValue] [varchar](max) NOT NULL,
	[Remarks] [varchar](1000) NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
	[TmplIndexGuid] [uniqueidentifier] NOT NULL,
	[SpecialTypeGuid] [uniqueidentifier] NOT NULL,
	[DisplayOrder] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[IndexGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_cdb_CupIndexSpecialType
CREATE TABLE [dbo].[cb_cdb_CupIndexSpecialType](
	[SpecialTypeGuid] [uniqueidentifier] NOT NULL,
	[TmplSpecialTypeGuid] [uniqueidentifier] NOT NULL,
	[ContractGuids] [varchar](max) NULL,
	[OperationalStage] [varchar](20) NULL,
	[RefGuid] [uniqueidentifier] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
	[DisplayOrder] [int] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[SpecialTypeGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_cdb_CupIndexSpecialTypeTmpl
CREATE TABLE [dbo].[cb_cdb_CupIndexSpecialTypeTmpl](
	[SpecialTypeGuid] [uniqueidentifier] NOT NULL,
	[SpecialTypeCode] [varchar](100) NOT NULL,
	[Name] [varchar](100) NOT NULL,
	[Level] [tinyint] NOT NULL,
	[IsLastLevel] [bit] NOT NULL,
	[IsEnabled] [bit] NOT NULL,
	[RefType] [tinyint] NOT NULL,
	[ParentCode] [varchar](100) NULL,
	[AccountingCostGuid] [uniqueidentifier] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[SpecialTypeGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_cdb_CupIndexTmpl
CREATE TABLE [dbo].[cb_cdb_CupIndexTmpl](
	[IndexGuid] [uniqueidentifier] NOT NULL,
	[SpecialTypeGuid] [uniqueidentifier] NOT NULL,
	[Name] [varchar](100) NOT NULL,
	[DisplayOrder] [int] NOT NULL,
	[IndexType] [varchar](20) NOT NULL,
	[IndexUnit] [varchar](20) NULL,
	[Remarks] [varchar](1000) NULL,
	[OptionsConfig] [varchar](max) NULL,
	[DataTypeConfig] [tinyint] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[UpdatedOn] [datetime] NOT NULL,
	[IsEnabled] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[IndexGuid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract01
CREATE TABLE [dbo].[cb_contract01](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract1
CREATE TABLE [dbo].[cb_contract1](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20200327bak
CREATE TABLE [dbo].[cb_contract20200327bak](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20210421
CREATE TABLE [dbo].[cb_contract20210421](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20220316
CREATE TABLE [dbo].[cb_contract20220316](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20220719bak
CREATE TABLE [dbo].[cb_contract20220719bak](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](20) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20240801
CREATE TABLE [dbo].[cb_contract20240801](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20241223
CREATE TABLE [dbo].[cb_contract20241223](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract2024801
CREATE TABLE [dbo].[cb_contract2024801](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20250224
CREATE TABLE [dbo].[cb_contract20250224](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_contract20250820
CREATE TABLE [dbo].[cb_contract20250820](
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NOT NULL,
	[HtTypeCode] [varchar](100) NULL,
	[HtKind] [varchar](10) NULL,
	[ContractCode] [varchar](400) NULL,
	[ContractName] [varchar](80) NULL,
	[HtClass] [varchar](16) NULL,
	[SignMode] [varchar](50) NULL,
	[CostProperty] [varchar](10) NULL,
	[Jbr] [varchar](20) NULL,
	[SignDate] [datetime] NULL,
	[JfCorporation] [varchar](20) NULL,
	[YfCorporation] [varchar](50) NULL,
	[BfCorporation] [varchar](20) NULL,
	[HtProperty] [varchar](16) NULL,
	[IfDdhs] [tinyint] NULL,
	[MasterContractGUID] [uniqueidentifier] NULL,
	[TotalAmount] [money] NULL,
	[BjcbAmount] [money] NULL,
	[ItemAmount] [money] NULL,
	[HtAmount] [money] NULL,
	[ItemDtAmount] [money] NULL,
	[HtycAmount] [money] NULL,
	[JsState] [varchar](10) NULL,
	[ZjsAmount] [money] NULL,
	[JsAmount] [money] NULL,
	[JsBxAmount] [money] NULL,
	[JsOtherDeduct] [money] NULL,
	[JsItemDeduct] [money] NULL,
	[LocaleAlterAmount] [money] NULL,
	[DesignAlterAmount] [money] NULL,
	[OtherAlterAmount] [money] NULL,
	[BalanceAdjustAmount] [money] NULL,
	[SumALterAmount] [money] NULL,
	[SumYfAmount] [money] NULL,
	[SumScheduleAmount] [money] NULL,
	[SumFactAmount] [money] NULL,
	[ConfirmJhfkAmount] [money] NULL,
	[IfConfirmFkPlan] [tinyint] NULL,
	[SumPayAmount] [money] NULL,
	[LandSource] [varchar](16) NULL,
	[LandUseLimit] [varchar](16) NULL,
	[BuildArea] [money] NULL,
	[LandProperty] [varchar](16) NULL,
	[LandUse] [varchar](16) NULL,
	[LandRemarks] [text] NULL,
	[BeginDate] [datetime] NULL,
	[EndDate] [datetime] NULL,
	[WorkPeriod] [int] NULL,
	[BxAmount] [money] NULL,
	[BxLimit] [varchar](16) NULL,
	[PerformBail] [money] NULL,
	[PerformRemarks] [text] NULL,
	[TechnicRemarks] [text] NULL,
	[RewardRemarks] [text] NULL,
	[BreachRemarks] [text] NULL,
	[TermRemarks] [text] NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[CfMode] [varchar](10) NULL,
	[YcfAmount] [money] NULL,
	[HtCfState] [varchar](10) NULL,
	[AlterCfState] [varchar](10) NULL,
	[FactCfAmount] [money] NULL,
	[FactCfState] [varchar](10) NULL,
	[PayCfState] [varchar](10) NULL,
	[ItemCfAmount] [money] NULL,
	[ItemCfState] [varchar](10) NULL,
	[HtycCfAmount] [money] NULL,
	[HtycCfState] [varchar](10) NULL,
	[FinanceHsxmCode] [varchar](20) NULL,
	[FinanceHsxmName] [varchar](60) NULL,
	[ApproveLog] [text] NULL,
	[ProcessStatusContract] [tinyint] NULL,
	[TacticProtocolGUID] [uniqueidentifier] NULL,
	[CgPlanGUID] [uniqueidentifier] NULL,
	[JfProviderGUID] [uniqueidentifier] NULL,
	[YfProviderGUID] [uniqueidentifier] NULL,
	[BfProviderGUID] [uniqueidentifier] NULL,
	[IsJtContract] [tinyint] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[ProjType] [varchar](20) NULL,
	[DeptGUID] [uniqueidentifier] NULL,
	[ContractCodeFormat] [varchar](800) NULL,
	[JfProviderName] [varchar](100) NULL,
	[YfProviderName] [varchar](100) NULL,
	[BfProviderName] [varchar](100) NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[SumScheduleAmount_Bz] [money] NULL,
	[SumPayAmount_Bz] [money] NULL,
	[SumAlterAmount_Bz] [money] NULL,
	[SumYfAmount_Bz] [money] NULL,
	[JsAmount_Bz] [money] NULL,
	[ZjsAmount_Bz] [money] NULL,
	[HtAmount_Bz] [money] NULL,
	[JsOtherDeduct_Bz] [money] NULL,
	[UseCostInfo] [varchar](max) NULL,
	[UseCostColor] [varchar](10) NULL,
	[IsLock] [tinyint] NULL,
	[ProjectCodeList] [varchar](4000) NULL,
	[ProjectNameList] [varchar](4000) NULL,
	[YgAlterAmount] [money] NULL,
	[YgAlterRemarks] [text] NULL,
	[YgAlterBudget] [text] NULL,
	[SchedulePayRate] [int] NULL,
	[ProjectPlanAffect] [tinyint] NULL,
	[UseStockInfo] [nvarchar](1000) NULL,
	[HsCfState] [varchar](10) NULL,
	[InvoiceAmount] [money] NULL,
	[SumYfBxAmount] [money] NULL,
	[isUseYgAmount] [tinyint] NOT NULL,
	[Contract2CgProcGUID] [uniqueidentifier] NULL,
	[IsFyControl] [tinyint] NULL,
	[ApplyGUID] [uniqueidentifier] NULL,
	[ApplySubject] [varchar](200) NULL,
	[InputTaxAmount] [money] NULL,
	[InputTaxAmount_Bz] [money] NULL,
	[ExcludingTaxHtAmount] [money] NULL,
	[ExcludingTaxHtAmount_Bz] [money] NULL,
	[AverageTaxRate] [decimal](18, 2) NULL,
	[IsPerformInvoiceControl] [tinyint] NULL,
	[ExcludingTaxYgAlterAmount] [money] NOT NULL,
	[ExcludingTaxLocaleAlterAmount] [money] NOT NULL,
	[ExcludingTaxDesignAlterAmount] [money] NOT NULL,
	[ExcludingTaxBalanceAdjustAmount] [money] NOT NULL,
	[ExcludingTaxOtherAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount_Bz] [money] NOT NULL,
	[ExcludingTaxSumAlterAmount] [money] NOT NULL,
	[ExcludingTaxSumScheduleAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct] [money] NOT NULL,
	[ExcludingTaxJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxJsAmount] [money] NOT NULL,
	[ExcludingTaxConfirmJhfkAmount] [money] NOT NULL,
	[ExcludingTaxJsBxAmount] [money] NOT NULL,
	[ExcludingTaxJsItemDeduct] [money] NOT NULL,
	[ExcludingTaxJsOtherDeduct_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount_Bz] [money] NOT NULL,
	[ExcludingTaxZJsAmount] [money] NOT NULL,
	[ExcludingTaxTotalAmount] [money] NOT NULL,
	[FtBeginDate] [datetime] NULL,
	[FtPeriod] [tinyint] NULL,
	[TemplateGUID] [uniqueidentifier] NULL,
	[ZbjAmount] [money] NULL,
	[ZbjRate] [money] NULL,
	[IsZbj] [tinyint] NULL,
	[ZbjStatus] [varchar](40) NULL,
	[ZbjShr] [varchar](40) NULL,
	[ZbjShDate] [datetime] NULL,
	[NewJbr] [varchar](50) NULL,
	[NewJbrGUID] [uniqueidentifier] NULL,
	[NewDeptGUID] [uniqueidentifier] NULL,
	[IsPaper] [varchar](20) NULL,
	[IsDemonstrationArea] [varchar](20) NULL,
	[PaymentMethod] [varchar](20) NULL,
	[ValuationMethod] [varchar](20) NULL,
	[ContractFormation] [varchar](20) NULL,
	[PaymentTerms] [varchar](600) NULL,
	[IsFromERP253] [tinyint] NULL,
	[IsJS] [tinyint] NULL,
	[YfProviderType] [varchar](100) NULL,
	[XjYc] [money] NULL,
	[SpBl] [money] NULL,
	[DfDc] [money] NULL,
	[IsJdJs] [tinyint] NOT NULL,
	[XYZJiaoFuRiQi] [datetime] NULL,
	[IsSendML] [int] NULL,
	[FwHtModuleID] [varchar](300) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_designalter20220825
CREATE TABLE [dbo].[cb_designalter20220825](
	[DesignAlterGuid] [uniqueidentifier] NOT NULL,
	[AlterCode] [varchar](800) NULL,
	[AlterName] [varchar](120) NULL,
	[AlterType] [varchar](20) NULL,
	[AlterReason] [varchar](30) NULL,
	[Remarks] [text] NULL,
	[ProjectInfo] [varchar](200) NULL,
	[ReportState] [varchar](30) NULL,
	[ApplyAmount] [money] NOT NULL,
	[DesignAlterAmount] [money] NOT NULL,
	[ApplyAmount_Bz] [money] NOT NULL,
	[DesignAlterAmount_Bz] [money] NOT NULL,
	[Bz] [varchar](20) NULL,
	[Rate] [money] NOT NULL,
	[ContractInfo] [varchar](2000) NULL,
	[Jbr] [varchar](30) NULL,
	[JbrGuid] [uniqueidentifier] NULL,
	[JbDeptGuid] [uniqueidentifier] NULL,
	[JbDept] [varchar](30) NULL,
	[ReportDate] [datetime] NULL,
	[ReportDeptGuid] [uniqueidentifier] NULL,
	[ReportDeptName] [varchar](200) NULL,
	[InvolveMajors] [varchar](50) NULL,
	[AlterLocation] [varchar](50) NULL,
	[ToPaperCode] [varchar](50) NULL,
	[CostBearedCompanyGuid] [uniqueidentifier] NULL,
	[CostBearedCompanyName] [varchar](200) NULL,
	[IsAffectSalePromise] [tinyint] NULL,
	[IsReturnWork] [tinyint] NULL,
	[CorrelativeDesignAlterGuid] [uniqueidentifier] NULL,
	[CorrelativeDesignAlterName] [varchar](200) NULL,
	[ReturnWorkAmount] [money] NOT NULL,
	[ReturnReason] [text] NULL,
	[ConnectFileGuid] [uniqueidentifier] NULL,
	[ApproveStatus] [varchar](20) NULL,
	[ApprovePassDate] [datetime] NULL,
	[ContractBudgetInfo] [varchar](500) NULL,
	[MidSubjectInfo] [varchar](500) NULL,
	[InvaildStatus] [varchar](10) NULL,
	[InvaildPersonGuid] [uniqueidentifier] NULL,
	[InvaildPerson] [varchar](30) NULL,
	[InvaildDate] [datetime] NULL,
	[InvaildReason] [varchar](500) NULL,
	[ContractGUIDList] [varchar](2000) NULL,
	[ProjGuidList] [varchar](2000) NULL,
	[DesignAlterCodeFormat] [varchar](800) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ProjCodeList] [varchar](2000) NULL,
	[ContractCodeList] [varchar](2000) NULL,
	[AlterTypeCode] [varchar](200) NOT NULL,
	[HTAlterNameList] [varchar](2000) NOT NULL,
	[HTAlterGuidList] [varchar](2000) NOT NULL,
	[InputTaxApplyAmount] [money] NULL,
	[InputTaxApplyAmount_Bz] [money] NULL,
	[InputTaxDesignAlterAmount] [money] NULL,
	[InputTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxApplyAmount] [money] NULL,
	[ExcludingTaxApplyAmount_Bz] [money] NULL,
	[ExcludingTaxDesignAlterAmount] [money] NULL,
	[ExcludingTaxDesignAlterAmount_Bz] [money] NULL,
	[ExcludingTaxReturnWorkAmount] [money] NOT NULL,
	[WgqrPension] [varchar](50) NULL,
	[WgqrPensionGUID] [uniqueidentifier] NULL,
	[DesignAlterAmountIsChanged] [tinyint] NULL,
	[ExcludingCbAmount] [money] NULL,
	[EstimatedAmount] [money] NULL,
	[InvalidCostAmount] [money] NULL,
	[IsYxys] [tinyint] NULL,
	[IsYcz] [tinyint] NULL,
	[IsJj] [tinyint] NULL,
	[IsGbsjfa] [tinyint] NULL,
	[IsYxgn] [tinyint] NULL,
	[IsCltz] [tinyint] NULL,
	[IsHb] [tinyint] NULL,
	[IsYyTc] [tinyint] NULL,
	[IsYxhtFt] [tinyint] NULL,
	[ImplementationTime] [datetime] NULL,
	[IsKzProgressPayment] [datetime] NULL,
	[JrWxCbYgAmount] [money] NULL,
	[IsYbGc] [int] NOT NULL,
	[JrWxCbAuditAmount] [money] NULL,
	[InvalidCostAuditAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule
CREATE TABLE [dbo].[cb_htschedule](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule20220727
CREATE TABLE [dbo].[cb_htschedule20220727](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule20240801
CREATE TABLE [dbo].[cb_htschedule20240801](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule20241008
CREATE TABLE [dbo].[cb_htschedule20241008](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule20241209
CREATE TABLE [dbo].[cb_htschedule20241209](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_htschedule20250224
CREATE TABLE [dbo].[cb_htschedule20250224](
	[HTScheduleGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[ApplyDate] [datetime] NULL,
	[ApplyAmount] [money] NULL,
	[Applicant] [varchar](20) NULL,
	[PerformRemarks] [text] NULL,
	[RefHtTerms] [text] NULL,
	[ApproveDocNo] [varchar](40) NULL,
	[ApproveState] [varchar](10) NULL,
	[ApproveDate] [datetime] NULL,
	[ApproveAmount] [money] NULL,
	[ApprovedBy] [varchar](20) NULL,
	[ItemAmount] [money] NULL,
	[OtherDeduct] [money] NULL,
	[ScheduleAmount] [money] NULL,
	[Budgeteer] [varchar](20) NULL,
	[BudgetDocNo] [varchar](20) NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[IfBalanceUsed] [tinyint] NULL,
	[Bz] [uniqueidentifier] NULL,
	[Rate] [decimal](18, 8) NULL,
	[ApplyAmount_Bz] [money] NULL,
	[ScheduleAmount_Bz] [money] NULL,
	[PayPercent] [decimal](18, 2) NULL,
	[ScheduleConsult] [money] NULL,
	[ExcludingTaxScheduleAmount] [money] NULL,
	[IsFromERP253] [tinyint] NULL,
	[LjApproveAmount] [money] NULL,
	[BqPlanAmount] [money] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_pay20190703
CREATE TABLE [dbo].[cb_pay20190703](
	[PayGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[VouchGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[PayMode] [varchar](16) NULL,
	[PayBankName] [varchar](30) NULL,
	[PayDate] [datetime] NULL,
	[PayAmount] [money] NULL,
	[Currency] [varchar](10) NULL,
	[Rate] [money] NULL,
	[PayAmount1] [money] NULL,
	[Ye] [money] NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[Status] [varchar](10) NULL,
	[PayRemarks] [text] NULL,
	[PrePayGUID] [uniqueidentifier] NULL,
	[FinanceJsMode] [varchar](30) NULL,
	[FinanceJsCode] [nvarchar](50) NULL,
	[PreCfPayGUID] [uniqueidentifier] NULL,
	[DcAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[LoanGUID] [uniqueidentifier] NULL,
	[HTFKApplyGUID] [uniqueidentifier] NULL,
	[FundSource] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ApproveState] [varchar](10) NULL,
	[ExcludingTaxPayAmount1] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_pay20190710
CREATE TABLE [dbo].[cb_pay20190710](
	[PayGUID] [uniqueidentifier] NOT NULL,
	[ContractGUID] [uniqueidentifier] NOT NULL,
	[VouchGUID] [uniqueidentifier] NOT NULL,
	[Num] [int] NULL,
	[FundType] [varchar](30) NULL,
	[FundName] [varchar](30) NULL,
	[PayMode] [varchar](16) NULL,
	[PayBankName] [varchar](30) NULL,
	[PayDate] [datetime] NULL,
	[PayAmount] [money] NULL,
	[Currency] [varchar](10) NULL,
	[Rate] [money] NULL,
	[PayAmount1] [money] NULL,
	[Ye] [money] NULL,
	[YcfAmount] [money] NULL,
	[CfState] [varchar](10) NULL,
	[Status] [varchar](10) NULL,
	[PayRemarks] [text] NULL,
	[PrePayGUID] [uniqueidentifier] NULL,
	[FinanceJsMode] [varchar](30) NULL,
	[FinanceJsCode] [nvarchar](50) NULL,
	[PreCfPayGUID] [uniqueidentifier] NULL,
	[DcAmount] [money] NULL,
	[FtMode] [tinyint] NULL,
	[LoanGUID] [uniqueidentifier] NULL,
	[HTFKApplyGUID] [uniqueidentifier] NULL,
	[FundSource] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[ApproveState] [varchar](10) NULL,
	[ExcludingTaxPayAmount1] [money] NOT NULL,
	[IsFromERP253] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_product_CsVersion
CREATE TABLE [dbo].[cb_product_CsVersion](
	[ProductCsVersionGUID] [uniqueidentifier] NULL,
	[VersionGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[ProductName] [varchar](40) NULL,
	[OccupyArea] [money] NULL,
	[SaleArea] [money] NULL,
	[BuildArea] [money] NULL,
	[InnerArea] [money] NULL,
	[Remarks] [text] NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[ProductShortCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[Level] [tinyint] NULL,
	[SaleNum] [int] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](100) NULL,
	[OccupyRate] [money] NULL,
	[BuildRate] [money] NULL,
	[SaleRate] [money] NULL,
	[InnerRate] [money] NULL,
	[ProductState] [varchar](20) NULL,
	[UnderArea] [money] NULL,
	[UpperArea] [money] NULL,
	[TargetCost] [money] NULL,
	[DtCost] [money] NULL,
	[CpFtBuildRate] [money] NULL,
	[CpFtOccupyRate] [money] NULL,
	[CpFtSaleRate] [money] NULL,
	[CpFtInnerRate] [money] NULL,
	[DateType] [varchar](16) NULL,
	[RelativelyDay] [int] NULL,
	[WorkGuid] [uniqueidentifier] NULL,
	[PlanSaleDate] [datetime] NULL,
	[PreGetSaleDate] [datetime] NULL,
	[PlanSalePeriod] [int] NULL,
	[RentableArea] [money] NULL,
	[FinishSaleArea] [money] NULL,
	[FinishSaleDate] [datetime] NULL,
	[PreNoSaleArea] [money] NULL,
	[IsSale] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[IsCYFT] [tinyint] NULL,
	[CpFtBuildAmount] [money] NULL,
	[CpFtOccupyAmount] [money] NULL,
	[CpFtSaleAmount] [money] NULL,
	[CpFtInnerAmount] [money] NULL,
	[IsLandTax] [tinyint] NULL,
	[IsDefence] [tinyint] NULL,
	[IsCar] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkAreaToCsfa
CREATE TABLE [dbo].[cb_sjkAreaToCsfa](
	[AreaGUID] [uniqueidentifier] NULL,
	[CsfaGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_sjkAreaToLsCbk
CREATE TABLE [dbo].[cb_sjkAreaToLsCbk](
	[AreaGUID] [uniqueidentifier] NULL,
	[LsCbkGUID] [uniqueidentifier] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_sjkBelongArea
CREATE TABLE [dbo].[cb_sjkBelongArea](
	[AreaGUID] [uniqueidentifier] NOT NULL,
	[AreaName] [varchar](100) NULL,
	[AreaShortName] [varchar](20) NULL,
	[AreaShortCode] [varchar](10) NULL,
	[AreaCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[AreaLevel] [tinyint] NULL,
	[IsXt] [tinyint] NULL,
	[IfEnd] [tinyint] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_sjkBelongAreaTemplet
CREATE TABLE [dbo].[cb_sjkBelongAreaTemplet](
	[AreaGUID] [uniqueidentifier] NOT NULL,
	[AreaShortName] [varchar](20) NULL,
	[AreaName] [varchar](100) NULL,
	[AreaShortCode] [varchar](10) NULL,
	[AreaCode] [varchar](50) NULL,
	[ParentCode] [varchar](50) NULL,
	[AreaLevel] [tinyint] NULL,
	[IsXt] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
 CONSTRAINT [PK_cb_sjkBelongAreaTemplet] PRIMARY KEY CLUSTERED 
(
	[AreaGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkCsfa
CREATE TABLE [dbo].[cb_sjkCsfa](
	[CsfaGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[CsfaName] [varchar](50) NULL,
	[CsfaCode] [varchar](10) NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjName] [varchar](400) NULL,
	[BelongAreaGUID] [uniqueidentifier] NULL,
	[BelongAreaName] [varchar](100) NULL,
	[CreateDate] [datetime] NULL,
	[ProjStatus] [varchar](20) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[CsMethod] [varchar](20) NULL,
	[CsYj] [text] NULL,
	[FaRemarks] [text] NULL,
	[ProjectType] [tinyint] NULL,
 CONSTRAINT [PK_cb_sjkCsfa] PRIMARY KEY CLUSTERED 
(
	[CsfaGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkCsfaAction
CREATE TABLE [dbo].[cb_sjkCsfaAction](
	[ActionGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[OrgGUID] [uniqueidentifier] NULL,
	[IsView] [tinyint] NULL,
	[IsEdit] [tinyint] NULL,
	[IsDelete] [tinyint] NULL,
	[IsJbr] [tinyint] NULL,
	[IsZjsq] [tinyint] NULL,
	[OrgType] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_sjkCsfaAction] PRIMARY KEY CLUSTERED 
(
	[ActionGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkDataCd
CREATE TABLE [dbo].[cb_sjkDataCd](
	[DataCdGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[DataCdName] [varchar](50) NULL,
	[DataCdCode] [varchar](10) NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjName] [varchar](400) NULL,
	[BelongAreaGUID] [uniqueidentifier] NULL,
	[BelongAreaName] [varchar](100) NULL,
	[CreateDate] [datetime] NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[DataCdStatus] [varchar](20) NULL,
	[Remarks ] [text] NULL,
 CONSTRAINT [PK_cb_sjkDataCd] PRIMARY KEY CLUSTERED 
(
	[DataCdGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkGhIndex
CREATE TABLE [dbo].[cb_sjkGhIndex](
	[IndexGUID] [uniqueidentifier] NOT NULL,
	[IndexName] [varchar](20) NULL,
	[IndexLevel] [tinyint] NULL,
	[IndexShortCode] [varchar](10) NULL,
	[IndexCode] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[GetValue] [varchar](50) NULL,
	[TableName] [varchar](20) NULL,
	[ColumnName] [varchar](20) NULL,
	[IndexType] [varchar](10) NULL,
	[IsXt] [tinyint] NULL,
	[Unit] [varchar](10) NULL,
	[Remarks] [text] NULL,
 CONSTRAINT [PK_cb_sjkGhIndex] PRIMARY KEY CLUSTERED 
(
	[IndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkGhIndexTemplet
CREATE TABLE [dbo].[cb_sjkGhIndexTemplet](
	[IndexName] [varchar](20) NULL,
	[IndexLevel] [tinyint] NULL,
	[IndexShortCode] [varchar](10) NULL,
	[IndexCode] [varchar](100) NULL,
	[ParentCode] [varchar](100) NULL,
	[GetValue] [varchar](50) NULL,
	[TableName] [varchar](20) NULL,
	[ColumnName] [varchar](20) NULL,
	[IndexType] [varchar](10) NULL,
	[IsXt] [tinyint] NULL,
	[Unit] [varchar](10) NULL,
	[Remarks] [text] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkHsCost
CREATE TABLE [dbo].[cb_sjkHsCost](
	[HsCostGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IsEndCost] [tinyint] NULL,
	[FtMode] [varchar](16) NULL,
	[IsJianAn] [tinyint] NULL,
	[IsForecast] [tinyint] NULL,
	[IsEndForecast] [tinyint] NULL,
	[TotalPrice] [money] NULL,
	[Remarks] [text] NULL,
	[GetMode] [varchar](20) NULL,
	[CsjcState] [tinyint] NULL,
	[CsjcCode] [varchar](100) NULL,
	[CsjcName] [varchar](50) NULL,
	[XsUnit] [varchar](20) NULL,
	[GclUnit] [varchar](20) NULL,
 CONSTRAINT [PK_cb_sjkHsCost] PRIMARY KEY NONCLUSTERED 
(
	[HsCostGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkJsjjIndex
CREATE TABLE [dbo].[cb_sjkJsjjIndex](
	[JsjjIndexGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[HsCostGUID] [uniqueidentifier] NULL,
	[CostCode] [varchar](100) NULL,
	[XsUnit] [varchar](20) NULL,
	[XsValue] [money] NULL,
	[GclUnit] [varchar](20) NULL,
	[GclValue] [money] NULL,
	[Price] [money] NULL,
	[TotalPrice] [money] NULL,
	[BuildCost] [money] NULL,
	[SaleCost] [money] NULL,
	[FactBuildCost] [money] NULL,
	[Remarks] [text] NULL,
	[CsjcName] [varchar](50) NULL,
	[GetMode] [varchar](20) NULL,
	[CsjcValue] [money] NULL,
	[CsjcIndexState] [tinyint] NULL,
	[CostLevel] [tinyint] NULL,
 CONSTRAINT [PK_cb_sjkJsjjIndex] PRIMARY KEY NONCLUSTERED 
(
	[JsjjIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkLsCbkAction
CREATE TABLE [dbo].[cb_sjkLsCbkAction](
	[ActionGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[OrgGUID] [uniqueidentifier] NULL,
	[IsView] [tinyint] NULL,
	[IsEdit] [tinyint] NULL,
	[IsDelete] [tinyint] NULL,
	[IsJbr] [tinyint] NULL,
	[IsZjsq] [tinyint] NULL,
	[OrgType] [varchar](10) NULL,
	[BUGUID] [uniqueidentifier] NULL,
 CONSTRAINT [PK_cb_sjkLsCbkAction] PRIMARY KEY CLUSTERED 
(
	[ActionGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkNtzProject
CREATE TABLE [dbo].[cb_sjkNtzProject](
	[NtzProjGUID] [uniqueidentifier] NOT NULL,
	[BUGUID] [uniqueidentifier] NULL,
	[NtzProjShortCode] [varchar](10) NULL,
	[NtzProjCode] [varchar](100) NULL,
	[ParentCode] [varchar](40) NULL,
	[Level ] [tinyint] NULL,
	[IfEnd] [tinyint] NULL,
	[ProjGUID] [uniqueidentifier] NULL,
	[ProjName] [varchar](100) NULL,
	[NtzProjShortName] [varchar](40) NULL,
	[NtzProjName] [varchar](400) NULL,
	[PlotName] [varchar](400) NULL,
	[ColumnName] [varchar](100) NULL,
	[OnceName] [varchar](400) NULL,
	[BelongAreaGUID] [uniqueidentifier] NULL,
	[BelongAreaName] [varchar](100) NULL,
	[ProjStatus] [varchar](20) NULL,
	[CreateTime] [datetime] NULL,
	[TzPrincipalGUID] [uniqueidentifier] NULL,
	[TzPrincipal] [varchar](20) NULL,
	[JbrGUID] [uniqueidentifier] NULL,
	[Jbr] [varchar](20) NULL,
	[TargetEffigy] [text] NULL,
	[Remarks] [text] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[SaleAmount] [money] NULL,
	[InnerArea] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
 CONSTRAINT [PK_cb_sjkNtzProject] PRIMARY KEY CLUSTERED 
(
	[NtzProjGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkProdHsCost
CREATE TABLE [dbo].[cb_sjkProdHsCost](
	[HsCostGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[CostShortCode] [varchar](10) NULL,
	[CostCode] [varchar](100) NULL,
	[CostShortName] [varchar](40) NULL,
	[ParentCode] [varchar](100) NULL,
	[CostLevel] [tinyint] NULL,
	[IsEndCost] [tinyint] NULL,
	[IsBzEndCost] [tinyint] NULL,
	[FtMode] [varchar](16) NULL,
	[IsJianAn] [tinyint] NULL,
	[IsForecast] [tinyint] NULL,
	[IsEndForecast] [tinyint] NULL,
	[TotalPrice] [money] NULL,
	[Remarks] [text] NULL,
	[GetMode] [varchar](20) NULL,
	[CsjcState] [tinyint] NULL,
	[CsjcCode] [varchar](100) NULL,
	[CsjcName] [varchar](50) NULL,
	[XsUnit] [varchar](20) NULL,
	[GclUnit] [varchar](20) NULL,
	[IsCyh] [tinyint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkProduct
CREATE TABLE [dbo].[cb_sjkProduct](
	[ProductGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ProductCode] [varchar](10) NULL,
	[ProductName] [varchar](50) NULL,
	[BProductTypeCode] [varchar](100) NULL,
	[IsSale] [tinyint] NULL,
	[Jgxs] [varchar](40) NULL,
	[Jcxs] [varchar](40) NULL,
	[Jzxs] [varchar](40) NULL,
	[Zjlx] [varchar](40) NULL,
	[Remarks] [text] NULL,
	[IsCYFT] [tinyint] NULL,
 CONSTRAINT [PK_cb_sjkProduct] PRIMARY KEY CLUSTERED 
(
	[ProductGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

-- TABLE: cb_sjkProductFtRule
CREATE TABLE [dbo].[cb_sjkProductFtRule](
	[ProductFtRuleGUID] [uniqueidentifier] NOT NULL,
	[HsCostGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[FtRate] [decimal](15, 10) NULL,
 CONSTRAINT [PK_cb_sjkProductFtRule] PRIMARY KEY NONCLUSTERED 
(
	[ProductFtRuleGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkProductIndex
CREATE TABLE [dbo].[cb_sjkProductIndex](
	[ProductIndexGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[BuildNum] [money] NULL,
	[FloorNum] [money] NULL,
	[CellNum] [money] NULL,
	[UserNum] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
	[GeneralHouseRate] [money] NULL,
	[NonGeneralHouseRate] [money] NULL,
	[GeneralHouseArea] [money] NULL,
	[NonGeneralHouseArea] [money] NULL,
	[CwPrice] [money] NULL,
	[GeneralHouseCoverArea] [money] NULL,
	[NoGeneralHouseCoverArea] [money] NULL,
	[OtherArea] [money] NULL,
	[OtherRate] [money] NULL,
	[OtherCoverArea] [money] NULL,
	[GeneralHouseCoverAreaRate] [money] NULL,
	[NoGeneralHouseCoverAreaRate] [money] NULL,
	[OtherCoverAreaRate] [money] NULL,
 CONSTRAINT [PK_cb_sjkProductIndex] PRIMARY KEY CLUSTERED 
(
	[ProductIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkProductIndexHistory
CREATE TABLE [dbo].[cb_sjkProductIndexHistory](
	[ProductIndexGUID] [uniqueidentifier] NOT NULL,
	[ProductGUID] [uniqueidentifier] NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[BuildNum] [money] NULL,
	[FloorNum] [money] NULL,
	[CellNum] [money] NULL,
	[UserNum] [money] NULL,
	[SaleNum] [money] NULL,
	[SalePrice] [money] NULL,
	[SaleAmount] [money] NULL,
 CONSTRAINT [PK_CB_SJKPRODUCTINDEXHISTORY] PRIMARY KEY CLUSTERED 
(
	[ProductIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO

-- TABLE: cb_sjkProjectIndex
CREATE TABLE [dbo].[cb_sjkProjectIndex](
	[ProjectIndexGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[AllTerraArea] [money] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[RoadArea] [money] NULL,
	[SightArea] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
	[YdlhArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[FactCubageRate] [money] NULL,
	[CwNum] [money] NULL,
	[UpperCwNum] [money] NULL,
	[UnderCwNum] [money] NULL,
	[ZjLength] [money] NULL,
	[GateNum] [money] NULL,
	[RjArea] [money] NULL,
	[RjAreaRate] [money] NULL,
	[YjArea] [money] NULL,
	[YjAreaRate] [money] NULL,
	[SjArea] [money] NULL,
	[SjAreaRate] [money] NULL,
	[QwsLength] [money] NULL,
	[QwdLength] [money] NULL,
	[QwqLength] [money] NULL,
	[QwrlLength] [money] NULL,
	[Kbsrj] [money] NULL,
	[Pdfrj] [money] NULL,
	[PdfNum] [money] NULL,
	[Fdjgl] [money] NULL,
	[WfQty] [money] NULL,
	[TfQty] [money] NULL,
	[WyQty] [money] NULL
) ON [PRIMARY]
GO

-- TABLE: cb_sjkProjectIndexHistory
CREATE TABLE [dbo].[cb_sjkProjectIndexHistory](
	[ProjectIndexGUID] [uniqueidentifier] NOT NULL,
	[RefGUID] [uniqueidentifier] NULL,
	[AllTerraArea] [money] NULL,
	[BuildTerraArea] [money] NULL,
	[InnerArea] [money] NULL,
	[OccupyArea] [money] NULL,
	[RoadArea] [money] NULL,
	[SightArea] [money] NULL,
	[CubageRate] [money] NULL,
	[BuildDensity] [money] NULL,
	[YdlhArea] [money] NULL,
	[BuildArea] [money] NULL,
	[UpperBuildArea] [money] NULL,
	[UnderBuildArea] [money] NULL,
	[SaleArea] [money] NULL,
	[UpperSaleArea] [money] NULL,
	[UnderSaleArea] [money] NULL,
	[LendArea] [money] NULL,
	[UpperLendArea] [money] NULL,
	[UnderLendArea] [money] NULL,
	[LargessArea] [money] NULL,
	[FactBuildArea] [money] NULL,
	[FactCubageRate] [money] NULL,
	[CwNum] [money] NULL,
	[UpperCwNum] [money] NULL,
	[UnderCwNum] [money] NULL,
	[ZjLength] [money] NULL,
	[GateNum] [money] NULL,
	[RjArea] [money] NULL,
	[RjAreaRate] [money] NULL,
	[YjArea] [money] NULL,
	[YjAreaRate] [money] NULL,
	[SjArea] [money] NULL,
	[SjAreaRate] [money] NULL,
	[QwsLength] [money] NULL,
	[QwdLength] [money] NULL,
	[QwqLength] [money] NULL,
	[QwrlLength] [money] NULL,
	[Kbsrj] [money] NULL,
	[Pdfrj] [money] NULL,
	[PdfNum] [money] NULL,
	[Fdjgl] [money] NULL,
	[WfQty] [money] NULL,
	[TfQty] [money] NULL,
	[WyQty] [money] NULL,
 CONSTRAINT [PK_CB_SJKPROJECTINDEXHISTORY] PRIMARY KEY NONCLUSTERED 
(
	[ProjectIndexGUID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 80) ON [PRIMARY]
) ON [PRIMARY]
GO


/*
=== DDL过滤摘要 ===
处理时间: 2025-08-25 13:44:51
输入文件: script.sql
过滤前缀: cb_
排除备份: True
目标数据库: dotnet_erp352SP4

=== 统计信息 ===
总对象数: 5821
过滤后对象数: 416
排除备份对象数: 49

=== 对象类型分布 ===
table: 416
*/